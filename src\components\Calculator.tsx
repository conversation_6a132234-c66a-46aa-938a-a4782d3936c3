import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Surface } from 'react-native-paper';

const Calculator = () => {
  const [display, setDisplay] = useState('0');
  const [memory, setMemory] = useState('0');
  const [operation, setOperation] = useState<string | null>(null);
  const [stepCheck, setStepCheck] = useState<string[]>([]);
  const [isNewNumber, setIsNewNumber] = useState(true);

  const handleNumber = (num: string) => {
    if (isNewNumber) {
      setDisplay(num);
      setIsNewNumber(false);
    } else {
      setDisplay(display + num);
    }
  };

  const handleOperation = (op: string) => {
    if (op === '=') {
      calculate();
      return;
    }
    
    setOperation(op);
    setMemory(display);
    setIsNewNumber(true);
    
    // Add to step check
    setStepCheck([...stepCheck, display]);
  };

  const calculate = () => {
    const prev = parseFloat(memory);
    const current = parseFloat(display);
    let result = 0;

    switch (operation) {
      case '+':
        result = prev + current;
        break;
      case '-':
        result = prev - current;
        break;
      case '×':
        result = prev * current;
        break;
      case '÷':
        result = prev / current;
        break;
    }

    setDisplay(result.toString());
    setStepCheck([...stepCheck, display, '=', result.toString()]);
    setIsNewNumber(true);
    setOperation(null);
  };

  const handleClear = () => {
    setDisplay('0');
    setMemory('0');
    setOperation(null);
    setStepCheck([]);
    setIsNewNumber(true);
  };

  const renderButton = (text: string, type: 'number' | 'operation' = 'number') => (
    <TouchableOpacity
      style={[
        styles.button,
        type === 'operation' ? styles.operationButton : styles.numberButton
      ]}
      onPress={() => type === 'number' ? handleNumber(text) : handleOperation(text)}
    >
      <Text style={[
        styles.buttonText,
        type === 'operation' ? styles.operationText : styles.numberText
      ]}>
        {text}
      </Text>
    </TouchableOpacity>
  );

  return (
    <Surface style={styles.container}>
      {/* Step Check Display */}
      <View style={styles.stepCheck}>
        <Text style={styles.stepText}>
          Step check ({stepCheck.length}) → {stepCheck.join(' ')}
        </Text>
      </View>

      {/* Main Display */}
      <View style={styles.display}>
        <Text style={styles.displayText}>{display}</Text>
      </View>

      {/* Keypad */}
      <View style={styles.keypad}>
        <View style={styles.row}>
          <TouchableOpacity style={styles.button} onPress={handleClear}>
            <Text style={styles.buttonText}>AC</Text>
          </TouchableOpacity>
          {renderButton('M+', 'operation')}
          {renderButton('M-', 'operation')}
          {renderButton('MRC', 'operation')}
          {renderButton('÷', 'operation')}
        </View>
        <View style={styles.row}>
          {renderButton('7')}
          {renderButton('8')}
          {renderButton('9')}
          {renderButton('×', 'operation')}
        </View>
        <View style={styles.row}>
          {renderButton('4')}
          {renderButton('5')}
          {renderButton('6')}
          {renderButton('-', 'operation')}
        </View>
        <View style={styles.row}>
          {renderButton('1')}
          {renderButton('2')}
          {renderButton('3')}
          {renderButton('+', 'operation')}
        </View>
        <View style={styles.row}>
          {renderButton('0')}
          {renderButton('.')}
          {renderButton('⌫')}
          {renderButton('=', 'operation')}
        </View>
        <View style={styles.bottomButtons}>
          <TouchableOpacity style={styles.expenseButton}>
            <Text style={styles.bottomButtonText}>Expense</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.salesButton}>
            <Text style={styles.bottomButtonText}>Sales/Bill</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  stepCheck: {
    padding: 8,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 8,
  },
  stepText: {
    fontSize: 12,
    color: '#666',
  },
  display: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    alignItems: 'flex-end',
  },
  displayText: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  keypad: {
    gap: 8,
  },
  row: {
    flexDirection: 'row',
    gap: 8,
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    aspectRatio: 1,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    elevation: 2,
  },
  numberButton: {
    backgroundColor: '#fff',
  },
  operationButton: {
    backgroundColor: '#e0e0e0',
  },
  buttonText: {
    fontSize: 24,
    fontWeight: '500',
  },
  numberText: {
    color: '#000',
  },
  operationText: {
    color: '#666',
  },
  bottomButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 16,
  },
  expenseButton: {
    flex: 1,
    backgroundColor: '#ff9800',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  salesButton: {
    flex: 1,
    backgroundColor: '#4caf50',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  bottomButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default Calculator; 