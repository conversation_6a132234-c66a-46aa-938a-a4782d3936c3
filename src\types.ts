export type InventoryItem = {
  id: string;
  name: string;
  quantity: number;
  price: number;
  description?: string;
  category: string;
  lastUpdated: Date;
};

export type BillingRecord = {
  id: string;
  customerName: string;
  amount: number;
  date: Date;
  items: string[];
};

export type TimePeriod = 'today' | 'weekly' | 'monthly' | 'yearly';

export type Stats = {
  totalBill: number;
  totalSales: number;
  inventoryStatus: string;
}; 