import * as React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { PaperProvider } from 'react-native-paper';
import DashboardScreen from './src/screens/DashboardScreen';
import InventoryScreen from './src/screens/InventoryScreen';
import BillingScreen from './src/screens/BillingScreen';
import MainTabNavigator from './src/navigation/MainTabNavigator';
import { InventoryProvider } from './src/contexts/InventoryContext';
import SalesScreen from './src/screens/SalesScreen';
import { SalesProvider } from './src/contexts/SalesContext';
import ReportScreen from './src/screens/ReportScreen';

const Stack = createNativeStackNavigator();

export default function App() {
  return (
    <PaperProvider>
      <SalesProvider>
        <InventoryProvider>
          <NavigationContainer>
            <Stack.Navigator>
              <Stack.Screen 
                name="Main" 
                component={MainTabNavigator} 
                options={{ headerShown: false }}
              />
              <Stack.Screen name="Inventory" component={InventoryScreen} />
              <Stack.Screen name="Billing" component={BillingScreen} />
              <Stack.Screen name="Bills" component={BillingScreen} />
              <Stack.Screen name="Sales" component={SalesScreen} />
              <Stack.Screen name="Expenses" component={BillingScreen} />
              <Stack.Screen name="Reports" component={ReportScreen} />
            </Stack.Navigator>
          </NavigationContainer>
        </InventoryProvider>
      </SalesProvider>
    </PaperProvider>
  );
}

// Navigation configuration in separate file (src/navigation/MainTabNavigator.tsx) 