                        -HC:\xampp\htdocs\calc - Copy\node_modules\expo-modules-core\android
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\xampp\htdocs\calc - Copy\node_modules\expo-modules-core\android\build\intermediates\cxx\Debug\1u3b6c5l\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\xampp\htdocs\calc - Copy\node_modules\expo-modules-core\android\build\intermediates\cxx\Debug\1u3b6c5l\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=C:\xampp\htdocs\calc - Copy\node_modules\expo-modules-core\android\.cxx\Debug\1u3b6c5l\prefab\armeabi-v7a\prefab
-BC:\xampp\htdocs\calc - Copy\node_modules\expo-modules-core\android\.cxx\Debug\1u3b6c5l\armeabi-v7a
-GNinja
-DANDROID_STL=c++_shared
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
-DREACT_NATIVE_DIR=C:\xampp\htdocs\calc - Copy\node_modules\react-native
-DREACT_NATIVE_TARGET_VERSION=79
-DUSE_HERMES=false
-DIS_NEW_ARCHITECTURE_ENABLED=true
-DUNIT_TEST=false
                        Build command args: []
                        Version: 2