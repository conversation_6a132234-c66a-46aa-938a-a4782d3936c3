[{"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\expo-modules-core\\common\\cpp\\EventEmitter.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\EventEmitter.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\EventEmitter.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\expo-modules-core\\common\\cpp\\JSIUtils.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\JSIUtils.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\JSIUtils.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\expo-modules-core\\common\\cpp\\LazyObject.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\LazyObject.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\LazyObject.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\expo-modules-core\\common\\cpp\\NativeModule.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\NativeModule.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\NativeModule.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\expo-modules-core\\common\\cpp\\ObjectDeallocator.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\ObjectDeallocator.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\ObjectDeallocator.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\expo-modules-core\\common\\cpp\\SharedObject.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\SharedObject.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\SharedObject.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\expo-modules-core\\common\\cpp\\SharedRef.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\SharedRef.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\SharedRef.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\expo-modules-core\\common\\cpp\\TypedArray.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\TypedArray.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\TypedArray.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\Exceptions.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\Exceptions.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\Exceptions.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\ExpoModulesHostObject.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\ExpoModulesHostObject.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\ExpoModulesHostObject.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JNIDeallocator.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIDeallocator.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIDeallocator.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JNIFunctionBody.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIFunctionBody.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIFunctionBody.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JNIInjector.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIInjector.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIInjector.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JNIUtils.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIUtils.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIUtils.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JSIContext.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSIContext.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSIContext.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JSReferencesCache.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSReferencesCache.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSReferencesCache.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JSharedObject.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSharedObject.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSharedObject.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaCallback.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaCallback.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaCallback.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaReferencesCache.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaReferencesCache.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaReferencesCache.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptFunction.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptFunction.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptFunction.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptModuleObject.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptModuleObject.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptModuleObject.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptObject.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptObject.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptObject.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptRuntime.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptRuntime.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptRuntime.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptTypedArray.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptTypedArray.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptTypedArray.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptValue.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptValue.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptValue.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptWeakObject.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptWeakObject.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptWeakObject.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\MethodMetadata.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\MethodMetadata.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\MethodMetadata.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\RuntimeHolder.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\RuntimeHolder.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\RuntimeHolder.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\WeakRuntimeHolder.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\WeakRuntimeHolder.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\WeakRuntimeHolder.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\AnyType.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\AnyType.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\AnyType.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\ExpectedType.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\ExpectedType.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\ExpectedType.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\FrontendConverter.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\FrontendConverter.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\FrontendConverter.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\FrontendConverterProvider.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\FrontendConverterProvider.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\FrontendConverterProvider.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\JNIToJSIConverter.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\JNIToJSIConverter.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\JNIToJSIConverter.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSClassesDecorator.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSClassesDecorator.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSClassesDecorator.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSConstantsDecorator.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSConstantsDecorator.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSConstantsDecorator.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSDecoratorsBridgingObject.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSDecoratorsBridgingObject.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSDecoratorsBridgingObject.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSFunctionsDecorator.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSFunctionsDecorator.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSFunctionsDecorator.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSObjectDecorator.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSObjectDecorator.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSObjectDecorator.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/src/fabric\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSPropertiesDecorator.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSPropertiesDecorator.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSPropertiesDecorator.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp/fabric\" -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewComponentDescriptor.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewComponentDescriptor.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewComponentDescriptor.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp/fabric\" -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewEventEmitter.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewEventEmitter.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewEventEmitter.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp/fabric\" -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewProps.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewProps.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewProps.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp/fabric\" -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewShadowNode.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewShadowNode.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewShadowNode.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/.cxx/Debug/1u3b6c5l/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/expo-modules-core/android/../common/cpp/fabric\" -IC:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/********************************/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\FabricComponentsRegistry.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\fabric\\FabricComponentsRegistry.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\fabric\\FabricComponentsRegistry.cpp"}]