ninja: Entering directory `C:\xampp\htdocs\calc - Copy\node_modules\react-native-screens\android\.cxx\Debug\b2vg565d\x86'
[1/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o
[2/6] Building CXX object CMakeFiles/rnscreens.dir/C_/xampp/htdocs/calc_-_Copy/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o
[3/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o
[4/6] Building CXX object CMakeFiles/rnscreens.dir/C_/xampp/htdocs/calc_-_Copy/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o
[5/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o
[6/6] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\b2vg565d\obj\x86\librnscreens.so
