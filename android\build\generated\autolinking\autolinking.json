{"root": "C:\\xampp\\htdocs\\calc - Copy", "reactNativePath": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-community/datetimepicker": {"root": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker", "name": "@react-native-community/datetimepicker", "platforms": {"android": {"sourceDir": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android", "packageImportPath": "import com.reactcommunity.rndatetimepicker.RNDateTimePickerPackage;", "packageInstance": "new RNDateTimePickerPackage()", "buildTypes": [], "libraryName": "RNDateTimePickerCGen", "componentDescriptors": [], "cmakeListsPath": "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/xampp/htdocs/calc - Copy/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-pager-view": {"root": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view", "name": "react-native-pager-view", "platforms": {"android": {"sourceDir": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android", "packageImportPath": "import com.reactnativepagerview.PagerViewPackage;", "packageInstance": "new PagerViewPackage()", "buildTypes": [], "libraryName": "pagerview", "componentDescriptors": ["RNCViewPagerComponentDescriptor"], "cmakeListsPath": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-pdf-lib": {"root": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf-lib", "name": "react-native-pdf-lib", "platforms": {"android": {"sourceDir": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf-lib\\android", "packageImportPath": "import com.hopding.pdflib.PDFLibPackage;", "packageInstance": "new PDFLibPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf-lib/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-share": {"root": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share", "name": "react-native-share", "platforms": {"android": {"sourceDir": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android", "packageImportPath": "import cl.json.RNSharePackage;", "packageInstance": "new RNSharePackage()", "buildTypes": [], "libraryName": "RNShareSpec", "componentDescriptors": [], "cmakeListsPath": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-vector-icons": {"root": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons", "name": "react-native-vector-icons", "platforms": {"android": {"sourceDir": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android", "packageImportPath": "import com.oblador.vectoricons.VectorIconsPackage;", "packageInstance": "new VectorIconsPackage()", "buildTypes": [], "libraryName": "RNVectorIconsSpec", "componentDescriptors": [], "cmakeListsPath": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.hisabi.Hisabi", "sourceDir": "C:\\xampp\\htdocs\\calc - Copy\\android"}}}