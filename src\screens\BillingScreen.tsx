import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, FlatList, TouchableOpacity } from 'react-native';
import { Text, Searchbar, Card, Button, IconButton, Checkbox, Portal, Modal, TextInput, DataTable } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { getProducts } from '../services/api';

const BillingScreen = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [products, setProducts] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [showExtraCharges, setShowExtraCharges] = useState(false);
  const [showBillSummary, setShowBillSummary] = useState(false);
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    phone: '',
    email: ''
  });
  const [showQuickAddModal, setShowQuickAddModal] = useState(false);
  const [showBillPreview, setShowBillPreview] = useState(false);
  const [quickAddItem, setQuickAddItem] = useState({
    name: '',
    price: '',
    quantity: '1'
  });
  const [customerDetails, setCustomerDetails] = useState({
    name: '',
    phone: '',
    address: ''
  });

  useEffect(() => {
    fetchProducts();
  }, []);

  useEffect(() => {
    calculateTotal();
  }, [selectedProducts]);

  const fetchProducts = async () => {
    try {
      const data = await getProducts();
      setProducts(data);
    } catch (error) {
      console.error('Error fetching products:', error);
    }
  };

  const addToCart = (product) => {
    const existingProduct = selectedProducts.find(item => item.id === product.id);
    
    if (existingProduct) {
      setSelectedProducts(selectedProducts.map(item => 
        item.id === product.id 
          ? { ...item, quantity: item.quantity + 1, total: (item.quantity + 1) * item.price }
          : item
      ));
    } else {
      const newProduct = {
        ...product,
        quantity: 1,
        total: product.price
      };
      setSelectedProducts([...selectedProducts, newProduct]);
    }
    setIsSearchFocused(false);
  };

  const updateQuantity = (id, increment) => {
    setSelectedProducts(selectedProducts.map(item => {
      if (item.id === id) {
        const newQuantity = increment ? item.quantity + 1 : Math.max(1, item.quantity - 1);
        return {
          ...item,
          quantity: newQuantity,
          total: newQuantity * item.price
        };
      }
      return item;
    }));
  };

  const removeItem = (id) => {
    setSelectedProducts(selectedProducts.filter(item => item.id !== id));
  };

  const calculateTotal = () => {
    const total = selectedProducts.reduce((sum, item) => sum + item.total, 0);
    setTotalAmount(total);
  };

  const handleAddQuickItem = () => {
    // Add custom item logic
    setShowExtraCharges(true);
  };

  const finalizeBill = () => {
    // Process bill completion logic
    navigation.goBack();
  };

  const handleQuickAdd = () => {
    const newItem = {
      id: Date.now(),
      name: quickAddItem.name,
      price: parseFloat(quickAddItem.price),
      quantity: parseInt(quickAddItem.quantity),
      total: parseFloat(quickAddItem.price) * parseInt(quickAddItem.quantity)
    };
    setSelectedProducts([...selectedProducts, newItem]);
    setShowQuickAddModal(false);
    setQuickAddItem({ name: '', price: '', quantity: '1' });
  };

  const handleBillSummary = () => {
    setShowBillPreview(true);
  };

  const processBill = () => {
    // Process the bill with customer details and items
    const billData = {
      customer: customerDetails,
      items: selectedProducts,
      total: totalAmount,
      date: new Date()
    };
    console.log('Processing bill:', billData);
    // Add API call here to save the bill
    navigation.goBack();
  };

  const renderItem = ({ item }) => (
    <Card style={styles.productCard}>
      <Card.Content style={styles.productContent}>
        <View style={styles.skuContainer}>
          <Text style={styles.skuText}>{item.sku || 'N/A'}</Text>
        </View>
        <View style={styles.productDetails}>
          <Text style={styles.productName}>{item.name}</Text>
          <Text style={styles.productPrice}>৳{item.price}</Text>
        </View>
        <Button 
          mode="contained" 
          onPress={() => addToCart(item)}
          style={styles.addButton}
          labelStyle={styles.addButtonLabel}
        >
          Add +
        </Button>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>


      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search by name or SKU ID"
          value={searchQuery}
          onChangeText={setSearchQuery}
          onFocus={() => setIsSearchFocused(true)}
          style={styles.searchBar}
        />
        <IconButton icon="filter-variant" size={24} />
        <IconButton icon="dots-vertical" size={24} />
      </View>

      {/* Always show the product list */}
      <View style={styles.productList}>
        <FlatList
          data={products.filter(product => 
            product.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (product.sku && product.sku.toLowerCase().includes(searchQuery.toLowerCase()))
          )}
          renderItem={renderItem}
          keyExtractor={item => item.id.toString()}
        />
      </View>

      {/* Selected Items List */}
      <ScrollView style={styles.selectedItems}>
        <DataTable>
          <DataTable.Header>
            <DataTable.Title>SKU</DataTable.Title>
            <DataTable.Title>Name</DataTable.Title>
            <DataTable.Title numeric>Price</DataTable.Title>
            <DataTable.Title numeric>Qty</DataTable.Title>
            <DataTable.Title numeric>Total</DataTable.Title>
            <DataTable.Title></DataTable.Title>
          </DataTable.Header>

          {selectedProducts.map(item => (
            <DataTable.Row key={item.id}>
              <DataTable.Cell>{item.sku || '-'}</DataTable.Cell>
              <DataTable.Cell>{item.name}</DataTable.Cell>
              <DataTable.Cell numeric>৳{item.price}</DataTable.Cell>
              <DataTable.Cell numeric>
                <View style={styles.quantityControl}>
                  <IconButton  size={20} onPress={() => updateQuantity(item.id, false)} />
                  <Text>{item.quantity}</Text>
                  <IconButton size={20} onPress={() => updateQuantity(item.id, true)} />
                </View>
              </DataTable.Cell>
              <DataTable.Cell numeric>৳{item.total}</DataTable.Cell>
              <DataTable.Cell>
                <IconButton icon="delete" size={20} onPress={() => removeItem(item.id)} />
              </DataTable.Cell>
            </DataTable.Row>
          ))}
        </DataTable>
      </ScrollView>

      {/* Footer Actions */}
      <View style={styles.footer}>
        <View style={styles.extraChargesRow}>
          <Checkbox
            status={showExtraCharges ? 'checked' : 'unchecked'}
            onPress={() => setShowExtraCharges(!showExtraCharges)}
          />
          <Text style={styles.extraChargesText}>Add Extra Charges</Text>
        </View>
        
        <View style={styles.quickAddContainer}>
          <Button
            mode="contained"
            icon="plus"
            onPress={() => setShowQuickAddModal(true)}
            style={styles.quickAddButton}
          >
            Quick Item Add
          </Button>
        </View>

        <View style={styles.totalSection}>
          <Text style={styles.totalAmount}>৳{totalAmount.toFixed(2)}</Text>
          <Text style={styles.totalItems}>{selectedProducts.length} items</Text>
        </View>

        <Button
          mode="contained"
          onPress={handleBillSummary}
          style={styles.summaryButton}
          disabled={selectedProducts.length === 0}
        >
          Bill Summary →
        </Button>
      </View>

      {/* Extra Charges Modal */}
      <Portal>
        <Modal
          visible={showExtraCharges}
          onDismiss={() => setShowExtraCharges(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Text style={styles.modalTitle}>Add Extra Charges</Text>
          <TextInput
            label="Charge Name"
            style={styles.input}
          />
          <TextInput
            label="Amount"
            keyboardType="numeric"
            style={styles.input}
          />
          <View style={styles.modalButtons}>
            <Button onPress={() => setShowExtraCharges(false)}>Cancel</Button>
            <Button mode="contained" onPress={() => setShowExtraCharges(false)}>Add</Button>
          </View>
        </Modal>
      </Portal>

      {/* Quick Add Item Modal */}
      <Portal>
        <Modal
          visible={showQuickAddModal}
          onDismiss={() => setShowQuickAddModal(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Text style={styles.modalTitle}>Quick Add Item</Text>
          <TextInput
            label="Item Name"
            value={quickAddItem.name}
            onChangeText={text => setQuickAddItem({ ...quickAddItem, name: text })}
            style={styles.input}
          />
          <TextInput
            label="Price"
            value={quickAddItem.price}
            onChangeText={text => setQuickAddItem({ ...quickAddItem, price: text })}
            keyboardType="numeric"
            style={styles.input}
          />
          <TextInput
            label="Quantity"
            value={quickAddItem.quantity}
            onChangeText={text => setQuickAddItem({ ...quickAddItem, quantity: text })}
            keyboardType="numeric"
            style={styles.input}
          />
          <View style={styles.modalButtons}>
            <Button onPress={() => setShowQuickAddModal(false)}>Cancel</Button>
            <Button mode="contained" onPress={handleQuickAdd}>Add</Button>
          </View>
        </Modal>
      </Portal>

      {/* Bill Summary Modal */}
      <Portal>
        <Modal
          visible={showBillPreview}
          onDismiss={() => setShowBillPreview(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Text style={styles.modalTitle}>Bill Summary</Text>
          
          <TextInput
            label="Customer Name"
            value={customerDetails.name}
            onChangeText={text => setCustomerDetails({ ...customerDetails, name: text })}
            style={styles.input}
          />
          
          <TextInput
            label="Phone Number"
            value={customerDetails.phone}
            onChangeText={text => setCustomerDetails({ ...customerDetails, phone: text })}
            keyboardType="phone-pad"
            style={styles.input}
          />
          
          <TextInput
            label="Address"
            value={customerDetails.address}
            onChangeText={text => setCustomerDetails({ ...customerDetails, address: text })}
            multiline
            style={styles.input}
          />
          
          <View style={styles.summarySection}>
            <Text style={styles.summaryTitle}>Items: {selectedProducts.length}</Text>
            <Text style={styles.summaryTotal}>Total Amount: ₹{totalAmount}</Text>
          </View>

          <View style={styles.modalButtons}>
            <Button onPress={() => setShowBillPreview(false)}>Back</Button>
            <Button mode="contained" onPress={processBill}>Process Bill</Button>
          </View>
        </Modal>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: '#fff',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: '#fff',
  },
  searchBar: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  columnHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  columnText: {
    fontSize: 14,
    color: '#666',
  },
  searchResults: {
    flex: 1,
    padding: 8,
    backgroundColor: '#fff',
  },
  productCard: {
    marginBottom: 8,
  },
  productContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  skuContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 8,
    marginRight: 12,
    width: 60,
    alignItems: 'center',
  },
  skuText: {
    fontSize: 14,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 14,
    color: '#666',
  },
  addButton: {
    backgroundColor: '#000',
    borderRadius: 4,
  },
  addButtonLabel: {
    fontSize: 12,
  },
  closeButton: {
    margin: 8,
  },
  selectedItems: {
    flex: 1,
    padding: 8,
  },
  quantityControl: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 28,
    height: 28,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButtonText: {
    fontSize: 16,
  },
  quantityText: {
    fontSize: 14,
    marginHorizontal: 8,
  },
  footer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#ddd',
  },
  extraChargesRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  extraChargesText: {
    fontSize: 14,
    marginLeft: 8,
  },
  quickAddContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  quickAddButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 20,
  },
  totalSection: {
    marginBottom: 16,
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalItems: {
    fontSize: 14,
    color: '#666',
  },
  summaryButton: {
    backgroundColor: '#000',
  },
  modalContainer: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 16,
  },
  summarySection: {
    marginVertical: 16,
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  summaryTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  summaryTotal: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  productList: {
    flex: 1,
    padding: 8,
  },
});

export default BillingScreen; 