import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, StyleSheet, RefreshControl, TouchableOpacity, FlatList } from 'react-native';
import { Text, Card, IconButton, Button, Searchbar, Switch, Portal, Modal, TextInput, Menu, Divider } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { useSales } from '../contexts/SalesContext';
import { format } from 'date-fns';
import { createExpense, getExpenseTypes, getProducts } from '../services/api';
import { Sale, Expense } from '../contexts/SalesContext'; // Import types
import Calculator from '../components/Calculator';

// First, let's define the ExpenseType interface
interface ExpenseType {
  id: number;
  name: string;
  // Add other properties as needed
}

// Add this interface at the top of your file with other interfaces
interface Product {
  id: number;
  name: string;
  price: number;
  stock: number;
  sku?: string;
  description?: string;
}

// Define the navigation type
type RootStackParamList = {
  Main: undefined;
  Inventory: undefined;
  Billing: undefined;
  Bills: undefined;
  Sales: undefined;
  Expenses: undefined;
  Reports: undefined;
};

const DashboardScreen = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [viewGraph, setViewGraph] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const { totalBalance, totalSales, totalExpenses, sales, expenses, refreshData } = useSales();
  const [showExpenseModal, setShowExpenseModal] = useState(false);
  const [expenseName, setExpenseName] = useState('');
  const [expenseAmount, setExpenseAmount] = useState('');
  const [expenseDescription, setExpenseDescription] = useState('');
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([]);
  const [filteredExpenseTypes, setFilteredExpenseTypes] = useState<ExpenseType[]>([]);
  const [selectedExpenseType, setSelectedExpenseType] = useState<ExpenseType | null>(null);
  const [showExpenseTypeMenu, setShowExpenseTypeMenu] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Sale | Expense | null>(null);
  const [showTransactionModal, setShowTransactionModal] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0, width: 0, height: 0 });
  const [expenseTypeSearch, setExpenseTypeSearch] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [showCalculator, setShowCalculator] = useState(false);

  useEffect(() => {
    refreshData();
    fetchExpenseTypes();
    fetchProducts();
  }, []);

  useEffect(() => {
    if (expenseTypes.length > 0) {
      if (expenseTypeSearch.trim() === '') {
        setFilteredExpenseTypes(expenseTypes);
      } else {
        const filtered = expenseTypes.filter(type => 
          type.name.toLowerCase().includes(expenseTypeSearch.toLowerCase())
        );
        setFilteredExpenseTypes(filtered);
      }
    }
  }, [expenseTypeSearch, expenseTypes]);

  const onRefresh = async () => {
    setRefreshing(true);
    await refreshData();
    setRefreshing(false);
  };

  const fetchExpenseTypes = async () => {
    try {
      const types = await getExpenseTypes();
      console.log('Expense types:', types);
      setExpenseTypes(types);
      setFilteredExpenseTypes(types);
      if (types.length > 0) {
        setSelectedExpenseType(types[0]);
      }
    } catch (error) {
      console.error('Error fetching expense types:', error);
    }
  };

  const fetchProducts = async () => {
    try {
      const productsData = await getProducts();
      setProducts(productsData);
    } catch (error) {
      console.error('Error fetching products:', error);
    }
  };

  const handleAddExpense = async () => {
    const amount = parseFloat(expenseAmount);
    if (amount > 0 && expenseName.trim() && selectedExpenseType) {
      try {
        await createExpense({
          title: expenseName,
          amount: amount,
          date: new Date().toISOString(),
          description: expenseDescription || expenseName,
          type: 'expense',
          expense_type_id: selectedExpenseType.id
        });
        
        // Clear form and close modal
        setExpenseName('');
        setExpenseAmount('');
        setExpenseDescription('');
        setShowExpenseModal(false);
        
        // Refresh dashboard data
        await refreshData();
      } catch (error) {
        console.error('Error adding expense:', error);
      }
    }
  };

  const handleTransactionPress = (transaction: Sale | Expense) => {
    setSelectedTransaction(transaction);
    setShowTransactionModal(true);
  };

  const renderTransactionDetails = () => {
    if (!selectedTransaction) return null;

    const isExpense = selectedTransaction.type === 'expense';
    const transaction = isExpense ? selectedTransaction as Expense : selectedTransaction as Sale;
    
    return (
      <Portal>
        <Modal
          visible={showTransactionModal}
          onDismiss={() => {
            setShowTransactionModal(false);
            setSelectedTransaction(null);
          }}
          contentContainerStyle={styles.modalContainer}
        >
          <ScrollView style={styles.modalScroll}>
            <Text style={styles.modalTitle}>
              {isExpense ? 'খরচের বিবরণ' : 'বিক্রয়ের বিবরণ'}
            </Text>

            {/* Common Details */}
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>তারিখ:</Text>
              <Text style={styles.detailValue}>
                {format(
                  new Date(transaction.date || (transaction as Sale).created_at || new Date()),
                  'dd MMM yyyy, HH:mm'
                )}
              </Text>
            </View>

            {isExpense ? (
              // Expense Details
              <>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>খরচের নাম:</Text>
                  <Text style={styles.detailValue}>
                    {(transaction as Expense).title || ''}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>পরিমাণ:</Text>
                  <Text style={styles.detailValue}>
                    ৳ {parseFloat((transaction as Expense).amount?.toString() || '0').toFixed(2)}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>বিবরণ:</Text>
                  <Text style={styles.detailValue}>
                    {(transaction as Expense).description || ''}
                  </Text>
                </View>
              </>
            ) : (
              // Sale Details
              <>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>বিক্রয় নং:</Text>
                  <Text style={styles.detailValue}>#{transaction.id}</Text>
                </View>

                {/* Customer Details Section */}
                <Text style={styles.sectionTitle}>গ্রাহকের তথ্য</Text>
                <View style={styles.detailCard}>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>নাম:</Text>
                    <Text style={styles.detailValue}>
                      {(transaction as Sale).customer_name || 'ওয়াক-ইন কাস্টমার'}
                    </Text>
                  </View>
                  {(transaction as Sale).customer_phone && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>ফোন:</Text>
                      <Text style={styles.detailValue}>
                        {(transaction as Sale).customer_phone}
                      </Text>
                    </View>
                  )}
                  {(transaction as Sale).customer_address && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>ঠিকানা:</Text>
                      <Text style={styles.detailValue}>
                        {(transaction as Sale).customer_address}
                      </Text>
                    </View>
                  )}
                </View>

                {/* Products Section */}
                <Text style={styles.sectionTitle}>পণ্যের তালিকা</Text>
                <View style={styles.detailCard}>
                  {(transaction as Sale).items && (transaction as Sale).items.length > 0 ? (
                    (transaction as Sale).items.map((item, index) => (
                      <View key={index} style={styles.productItem}>
                        <Text style={styles.productName}>
                          {item.product_name || `Product #${item.product_id}`}
                        </Text>
                        <View style={styles.productDetails}>
                          <Text style={styles.productQty}>পরিমাণ: {item.quantity}</Text>
                          <Text style={styles.productPrice}>দাম: ৳{item.unit_price}</Text>
                          <Text style={styles.productTotal}>মোট: ৳{item.subtotal || (item.quantity * item.unit_price)}</Text>
                        </View>
                      </View>
                    ))
                  ) : (
                    <Text>কোন পণ্য পাওয়া যায়নি</Text>
                  )}
                </View>

                {/* Payment Details */}
                <Text style={styles.sectionTitle}>পেমেন্ট বিবরণ</Text>
                <View style={styles.detailCard}>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>মোট মূল্য:</Text>
                    <Text style={styles.detailValue}>
                      ৳ {parseFloat((transaction as Sale).total?.toString() || '0').toFixed(2)}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>পেমেন্ট স্ট্যাটাস:</Text>
                    <Text style={[
                      styles.detailValue,
                      { color: (transaction as Sale).payment_status === 'paid' ? '#4CAF50' : '#F44336' }
                    ]}>
                      {(transaction as Sale).payment_status === 'paid' ? 'পরিশোধিত' : 'বাকি আছে'}
                    </Text>
                  </View>
                </View>
              </>
            )}

            <Button
              mode="contained"
              onPress={() => {
                setShowTransactionModal(false);
                setSelectedTransaction(null);
              }}
              style={styles.closeButton}
            >
              বন্ধ করুন
            </Button>
          </ScrollView>
        </Modal>
      </Portal>
    );
  };

  const renderTransactionItem = (item: Sale | Expense) => {
    const isExpense = item.type === 'expense';
    return (
      <TouchableOpacity 
        key={item.id}
        onPress={() => handleTransactionPress(item)}
        activeOpacity={0.7}
      >
        <Card style={styles.transactionCard}>
          <Card.Content style={styles.transactionContent}>
            <View style={styles.transactionLeft}>
              <View style={[styles.transactionIcon, { backgroundColor: isExpense ? '#FFEBEE' : '#E8F5E9' }]}>
                <IconButton 
                  icon={isExpense ? "minus" : "cash"} 
                  size={20}
                  iconColor={isExpense ? '#F44336' : '#4CAF50'}
                />
              </View>
              <View>
                <Text style={styles.transactionTitle}>
                  {isExpense ? (item as Expense).title : `বিক্রয় #${item.id}`}
                </Text>
                
                <Text style={styles.transactionDate}>
                  {format(new Date(item.date || (item as Sale).created_at || new Date()), 'dd MMM yyyy')}
                </Text>
              </View>
            </View>
            <Text style={[
              styles.transactionAmount,
              { color: isExpense ? '#F44336' : '#4CAF50' }
            ]}>
              ৳ {parseFloat(isExpense ? (item as Expense).amount.toString() : (item as Sale).total.toString()).toFixed(2)}
            </Text>
          </Card.Content>
        </Card>
      </TouchableOpacity>
    );
  };

  const measureDropdown = (event: any) => {
    const { x, y, width, height } = event.nativeEvent.layout;
    setMenuPosition({ x, y, width, height });
  };

  const openExpenseTypeModal = () => {
    setExpenseTypeSearch('');
    setFilteredExpenseTypes(expenseTypes);
    setShowExpenseModal(false);
    setTimeout(() => {
      setShowExpenseTypeMenu(true);
    }, 300);
  };

  const handleSelectExpenseType = (type: ExpenseType) => {
    setSelectedExpenseType(type);
    setShowExpenseTypeMenu(false);
    setTimeout(() => {
      setShowExpenseModal(true);
    }, 300);
  };

  const handleCloseTypeModal = () => {
    setShowExpenseTypeMenu(false);
    setTimeout(() => {
      setShowExpenseModal(true);
    }, 300);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Top Search Bar */}
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder="নাম/মোবাইল/আইডি অনুসন্ধান"
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
          />
          <IconButton icon="filter-outline" size={24} />
        </View>

        {/* View Controls */}
        <View style={styles.viewControls}>
          <Text>Manage view</Text>
          <View style={styles.dropdown}>
            <Text>Today</Text>
            <IconButton icon="chevron-down" size={20} />
          </View>
          <View style={styles.graphToggle}>
            <Text>View graph</Text>
            <Switch value={viewGraph} onValueChange={setViewGraph} />
          </View>
        </View>

        {/* Balance Card */}
        <Card style={styles.balanceCard}>
          <Card.Content>
            <Text style={styles.balanceTitle}>মোট ব্যালেন্স</Text>
            <Text style={styles.balanceAmount}>৳ {totalBalance.toFixed(2)}</Text>
            <Text style={styles.balanceBreakdown}>
              ক্যাশ: ৳ 1536.0 • বিকাশ: ৳ 1889.0 • বাকি: ৳ 785.0
            </Text>
            
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <IconButton icon="trending-up" size={24} iconColor="#4CAF50" />
                <Text>৳ {totalSales.toFixed(2)}</Text>
                <Text style={styles.statLabel}>মোট বিক্রয়</Text>
              </View>
              <View style={styles.statItem}>
                <IconButton icon="trending-down" size={24} iconColor="#F44336" />
                <Text>৳ {totalExpenses.toFixed(2)}</Text>
                <Text style={styles.statLabel}>মোট খরচ</Text>
              </View>
              <View style={styles.statItem}>
                <IconButton icon="file-document" size={24} iconColor="#2196F3" />
                <Text>৳ -</Text>
                <Text style={styles.statLabel}>মোট বিল</Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Transactions Section */}
        <View style={styles.transactionsSection}>
          <Text style={styles.sectionTitle}>লেনদেন দেখানো হচ্ছে</Text>
          <Text style={styles.dateLabel}>{format(new Date(), 'MMMM yyyy').toUpperCase()}</Text>
          
          <ScrollView style={styles.transactionsList}>
            {[
              ...sales.map(sale => ({ ...sale, type: 'sale' })),
              ...expenses.map(expense => ({ ...expense, type: 'expense' }))
            ]
            .sort((a, b) => {
              const dateA = new Date(a.date || (a.type === 'sale' ? (a as Sale).created_at : new Date()));
              const dateB = new Date(b.date || (b.type === 'sale' ? (b as Sale).created_at : new Date()));
              return dateB.getTime() - dateA.getTime(); // Sort in descending order (newest first)
            })
            .map(renderTransactionItem)}
          </ScrollView>
        </View>

        {/* Products Section */}
        <Card style={styles.card}>
          <Card.Title title="পণ্যের তালিকা" />
          <Card.Content>
            {products.length > 0 ? (
              <FlatList
                data={products}
                keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
                renderItem={({ item }) => (
                  <View style={styles.productItem}>
                    <Text style={styles.productName}>{item.name || 'Unnamed Product'}</Text>
                    <View style={styles.productDetails}>
                      <Text style={styles.productPrice}>৳{item.price || 0}</Text>
                      <Text style={styles.productQty}>স্টক: {item.stock || 0}</Text>
                    </View>
                  </View>
                )}
                style={{ maxHeight: 300 }}
              />
            ) : (
              <Text>কোন পণ্য পাওয়া যায়নি</Text>
            )}
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Fixed Bottom Navigation */}
      <View style={styles.bottomNav}>
        <Button 
          key="expense"
          mode="contained" 
          icon="minus"
          style={[styles.bottomButton, styles.expenseButton]}
          contentStyle={styles.buttonContent}
          labelStyle={styles.buttonLabel}
          onPress={() => setShowExpenseModal(true)}
        >
          Expenses
        </Button>
        <Button 
          key="bills"
          mode="contained"
          icon="file-document"
          style={[styles.bottomButton, styles.billsButton]}
          contentStyle={styles.buttonContent}
          labelStyle={styles.buttonLabel}
          onPress={() => navigation.navigate('Bills')}
        >
          Bills
        </Button>
        <Button 
          key="sales"
          mode="contained"
          icon="plus"
          style={[styles.bottomButton, styles.salesButton]}
          contentStyle={styles.buttonContent}
          labelStyle={styles.buttonLabel}
          onPress={() => navigation.navigate('Sales')}
        >
          Sales
        </Button>
      </View>

      {/* FAB */}
      <IconButton
        icon="calculator"
        mode="contained"
        style={styles.fab}
        size={24}
        onPress={() => setShowCalculator(true)}
      />

      {/* Expense Modal */}
      <Portal>
        <Modal
          visible={showExpenseModal}
          onDismiss={() => setShowExpenseModal(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Text style={styles.modalTitle}>Add New Expense</Text>
          
          <TextInput
            label="Expense Name"
            value={expenseName}
            onChangeText={setExpenseName}
            style={styles.input}
          />
          
          <TextInput
            label="Amount"
            value={expenseAmount}
            onChangeText={setExpenseAmount}
            keyboardType="numeric"
            style={styles.input}
          />
          
          <TextInput
            label="Description (Optional)"
            value={expenseDescription}
            onChangeText={setExpenseDescription}
            style={styles.input}
            multiline
          />
          
          {/* Expense Type Dropdown */}
          <View style={styles.dropdownContainer}>
            <Text style={styles.dropdownLabel}>Expense Type</Text>
            <TouchableOpacity 
              style={styles.dropdown}
              onPress={openExpenseTypeModal}
              onLayout={measureDropdown}
            >
              <Text>{selectedExpenseType ? selectedExpenseType.name : 'Select Expense Type'}</Text>
              <IconButton icon="menu-down" size={20} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.modalButtons}>
            <Button onPress={() => setShowExpenseModal(false)}>Cancel</Button>
            <Button 
              mode="contained" 
              onPress={handleAddExpense}
              disabled={!expenseName || !expenseAmount || !selectedExpenseType}
            >
              Add Expense
            </Button>
          </View>
        </Modal>
      </Portal>

      {/* Expense Type Selection Modal */}
      <Modal
        visible={showExpenseTypeMenu}
        onDismiss={handleCloseTypeModal}
        contentContainerStyle={styles.typeModalContainer}
      >
        <Text style={styles.modalTitle}>Select Expense Type</Text>
        
        <Searchbar
          placeholder="Search expense types"
          onChangeText={setExpenseTypeSearch}
          value={expenseTypeSearch}
          style={styles.searchBarModal}
        />
        
        <FlatList
          data={filteredExpenseTypes}
          keyExtractor={(item) => item.id.toString()}
          style={styles.typeList}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.typeItem}
              onPress={() => handleSelectExpenseType(item)}
            >
              <Text style={styles.typeName}>{item.name}</Text>
            </TouchableOpacity>
          )}
          ItemSeparatorComponent={() => <Divider />}
          ListEmptyComponent={() => (
            <Text style={styles.emptyList}>No expense types found</Text>
          )}
        />
        
        <Button 
          mode="outlined" 
          onPress={handleCloseTypeModal}
          style={styles.closeButton}
        >
          Close
        </Button>
      </Modal>

      {renderTransactionDetails()}

      {/* Calculator Modal */}
      <Portal>
        <Modal
          visible={showCalculator}
          onDismiss={() => setShowCalculator(false)}
          contentContainerStyle={styles.calculatorModal}
        >
          <Calculator />
        </Modal>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    paddingBottom: 16, // Add some padding at the bottom
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 0,
  },
  searchBar: {
    flex: 1,
    marginRight: 8,
    backgroundColor: 'white',
  },
  viewControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 3,
  },
  dropdown: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  graphToggle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  balanceCard: {
    margin: 16,
    backgroundColor: 'white',
  },
  balanceTitle: {
    fontSize: 16,
    color: '#666',
  },
  balanceAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    marginVertical: 8,
  },
  balanceBreakdown: {
    fontSize: 12,
    color: '#666',
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 0,
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  transactionsSection: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    color: '#666',
  },
  dateLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  transactionsList: {
    marginTop: 16,
  },
  transactionCard: {
    marginBottom: 8,
    marginHorizontal: 5,
    elevation: 2, // Add elevation for better touch feedback
  },
  transactionContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transactionIcon: {
    borderRadius: 20,
    marginRight: 12,
  },
  transactionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  transactionDate: {
    fontSize: 12,
    color: '#666',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  bottomNav: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 8,
    backgroundColor: 'white',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    elevation: 8,
  },
  bottomButton: {
    flex: 1,
    marginHorizontal: 4,
    borderRadius: 4,
  },
  expenseButton: {
    backgroundColor: '#F44336',
  },
  billsButton: {
    backgroundColor: '#2196F3',
  },
  salesButton: {
    backgroundColor: '#4CAF50',
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 80,
    backgroundColor: '#4CAF50',
  },
  buttonContent: {
    height: 48,
    flexDirection: 'row-reverse'
  },
  buttonLabel: {
    fontSize: 12,
    marginHorizontal: 4,
    width: 100,
  },
  modalContainer: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  dropdownContainer: {
    marginBottom: 12,
  },
  dropdownLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  dropdownMenu: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 8,
    paddingLeft: 12,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    flex: 2,
    textAlign: 'right',
  },
  closeButton: {
    marginTop: 24,
  },
  modalScroll: {
    maxHeight: '80%',
  },
  detailCard: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  productItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    paddingVertical: 8,
  },
  productName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  productDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  productQty: {
    fontSize: 14,
    color: '#666',
  },
  productPrice: {
    fontSize: 14,
    color: '#666',
  },
  productTotal: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  typeModalContainer: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
    zIndex: 1000,
    elevation: 5,
  },
  searchBarModal: {
    marginBottom: 10,
  },
  typeList: {
    maxHeight: 300,
  },
  typeItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  typeName: {
    fontSize: 16,
  },
  emptyList: {
    textAlign: 'center',
    padding: 20,
    color: '#666',
  },
  card: {
    margin: 16,
    backgroundColor: 'white',
  },
  calculatorModal: {
    margin: 0,
    backgroundColor: 'white',
    padding: 0,
    flex: 1,
  },
});

export default DashboardScreen; 