import React, { createContext, useContext, useState, useEffect } from 'react';
import { getSales, getExpenses } from '../services/api';

export type SaleItem = {
  id: number;
  product_id: number;
  product_name: string;
  quantity: number;
  price: number;
  total: number;
};

export type Sale = {
  id: number;
  items: SaleItem[];
  total: number;
  date: Date;
  payment_status: string;
  status: string;
  created_at: string;
  type?: string;
  customer_name?: string;
  customer_phone?: string;
  customer_address?: string;
};

export type Expense = {
  id: number;
  title: string;
  amount: number;
  date: Date;
  description: string;
  type: string;
};

type SalesContextType = {
  sales: Sale[];
  expenses: Expense[];
  addSale: (sale: Sale) => void;
  totalBalance: number;
  totalSales: number;
  totalExpenses: number;
  refreshData: () => Promise<void>;
};

const SalesContext = createContext<SalesContextType>({
  sales: [],
  expenses: [],
  addSale: () => {},
  totalBalance: 0,
  totalSales: 0,
  totalExpenses: 0,
  refreshData: async () => {},
});

export const SalesProvider = ({ children }) => {
  const [sales, setSales] = useState<Sale[]>([]);
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [totalSales, setTotalSales] = useState(0);
  const [totalExpenses, setTotalExpenses] = useState(0);
  const [totalBalance, setTotalBalance] = useState(0);

  const refreshData = async () => {
    try {
      const [salesData, expensesData] = await Promise.all([
        getSales(),
        getExpenses()
      ]);

      setSales(salesData);
      setExpenses(expensesData);
      
      // Calculate totals
      const salesTotal = salesData.reduce((sum, sale) => sum + (parseFloat(sale.total) || 0), 0);
      const expensesTotal = expensesData.reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);
      
      setTotalSales(salesTotal);
      setTotalExpenses(expensesTotal);
      setTotalBalance(salesTotal - expensesTotal);
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  };

  useEffect(() => {
    refreshData();
  }, []);

  const addSale = async (sale: Sale) => {
    await refreshData(); // Refresh all data after adding a sale
  };

  return (
    <SalesContext.Provider value={{ 
      sales, 
      expenses,
      addSale, 
      totalBalance,
      totalSales,
      totalExpenses,
      refreshData 
    }}>
      {children}
    </SalesContext.Provider>
  );
};

export const useSales = () => useContext(SalesContext); 