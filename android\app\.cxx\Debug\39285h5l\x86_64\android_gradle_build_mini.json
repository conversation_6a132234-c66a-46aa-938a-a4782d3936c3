{"buildFiles": ["C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp\\htdocs\\calc - Copy\\android\\app\\.cxx\\Debug\\39285h5l\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp\\htdocs\\calc - Copy\\android\\app\\.cxx\\Debug\\39285h5l\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"artifactName": "react_codegen_RNVectorIconsSpec", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "x86_64", "output": "C:\\xampp\\htdocs\\calc - Copy\\android\\app\\build\\intermediates\\cxx\\Debug\\39285h5l\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c52e289df292dc4688ccb75a4fa9a38\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_rnpdf::@7ef4ba29a735fbf48be7": {"artifactName": "react_codegen_rnpdf", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_pagerview::@7032a8921530ec438d60": {"artifactName": "react_codegen_pagerview", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "x86_64", "output": "C:\\xampp\\htdocs\\calc - Copy\\android\\app\\build\\intermediates\\cxx\\Debug\\39285h5l\\obj\\x86_64\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c52e289df292dc4688ccb75a4fa9a38\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}, "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2": {"artifactName": "react_codegen_RNDateTimePickerCGen", "abi": "x86_64", "runtimeFiles": []}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "x86_64", "output": "C:\\xampp\\htdocs\\calc - Copy\\android\\app\\build\\intermediates\\cxx\\Debug\\39285h5l\\obj\\x86_64\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c52e289df292dc4688ccb75a4fa9a38\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_RNShareSpec::@e6c4c2b5aedb2ea213b7": {"artifactName": "react_codegen_RNShareSpec", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "x86_64", "runtimeFiles": []}}}