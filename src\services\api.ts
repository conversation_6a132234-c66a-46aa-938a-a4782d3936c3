import axios from 'axios';

const BASE_URL = 'https://app.saudabari.com/api';  // Remove trailing slash
//const BASE_URL = ' http://*************/inventory/api';  // Replace with your local IP address  
//const BASE_URL = 'http://*************:8001/api';
//const BASE_URL = 'http://localhost:8001/api';

// Define interfaces for our data types
export interface Product {
  id: number;
  name: string;
  price: number;
  stock: number;
  sku?: string;
  description?: string;
}

interface ProductData {
  name: string;
  sku?: string | null;
  price: number;
  initial_stock: number;
  category_id: number;
  supplier_id: number;
  description?: string | null;
}

interface SaleItem {
  product_id: number;
  quantity: number;
  price: number;
  total: number;
  unit_price: number;
  discount?: number;
  tax?: number;
}

interface SaleData {
  total: number;
  tax?: number;
  discount?: number;
  items: SaleItem[];
  payment_status?: string;
  status?: string;
  date?: string;
  customer_id?: number;
  payment_method?: string;
  payment_note?: string;
  notes?: string;
}

export const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    // Add CSRF token if required by Larave
    // 'X-CSRF-TOKEN': 'your-csrf-token'
  },
  timeout: 30000, // Increase timeout to 30 seconds
  validateStatus: (status) => status < 500, // Accept all responses < 500
  withCredentials: true, // Include credentials
});

// Add request interceptor to handle errors
api.interceptors.request.use(
  (config) => {
    // Add timestamp to prevent caching
    config.params = {
      ...config.params,
      _t: Date.now()
    };
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Response Error:', error);
    if (error.code === 'ECONNABORTED') {
      // Handle timeout
      return Promise.reject(new Error('Request timed out'));
    }
    if (!error.response) {
      // Handle network error
      return Promise.reject(new Error('Network error - please check your connection'));
    }
    return Promise.reject(error);
  }
);

export const getProducts = async (): Promise<Product[]> => {
  try {
    const response = await api.get('/products');
    console.log('Products API Response:', response.data);
    
    // Handle different response formats
    let productsData = [];
    
    if (response.data && Array.isArray(response.data)) {
      // Direct array response
      productsData = response.data;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
      // Paginated response with data property
      productsData = response.data.data;
    } else if (response.data && typeof response.data === 'object') {
      // Object response with products as properties
      productsData = Object.values(response.data);
    }
    
    // Map the products to ensure consistent structure
    return productsData.map((product: any) => ({
      id: product.id || Math.random(),
      name: product.name || 'Unnamed Product',
      price: parseFloat(product.price?.toString() || '0'),
      stock: parseInt(product.stock_quantity?.toString() || '0'),
      sku: product.SKU || product.sku || product.code || '',
      description: product.description || ''
    }));
    
  } catch (error) {
    console.error('Error fetching products:', error);
    return []; // Return empty array on error
  }
};

export const addProduct = async (productData: ProductData) => {
  try {
    // Format data according to Laravel API requirements
    const formattedData = {
      name: productData.name,
      sku: productData.sku || null,
      price: Number(productData.price),
      initial_stock: Number(productData.initial_stock),
      category_id: Number(productData.category_id),
      supplier_id: Number(productData.supplier_id),
      description: productData.description || null
    };

    console.log('Sending data:', formattedData); // Debug log

    const response = await api.post('/products', formattedData);
    
    if (response.data.errors) {
      throw new Error(Object.values(response.data.errors).flat().join(', '));
    }
    
    return response.data;
  } catch (error: unknown) {
    let errorMessage = 'Failed to add product';
    
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    
    if (axios.isAxiosError(error) && error.response?.data?.message) {
      errorMessage = error.response.data.message;
    }
    
    console.error('Error adding product:', error);
    throw new Error(errorMessage);
  }
};

export const createSale = async (saleData: SaleData) => {
  try {
    // Format the data to match your Laravel backend expectations
    const formattedData = {
      // Basic sale information
      date: saleData.date || new Date().toISOString(),
      customer_id: saleData.customer_id || 1,
      status: saleData.status || 'completed',
      payment_status: saleData.payment_status || 'paid',
      payment_method: saleData.payment_method || 'cash',
      shop_id: 1,
      warehouse_id: 1,
      total_price: saleData.total,
      paid_amount: saleData.total,
      due_amount: 0,
      notes: saleData.notes || '',
      user_id: 1,
      
      // Financial details
      tax: saleData.tax || 0,
      discount: saleData.discount || 0,
      total: saleData.total,
      
      // Sale items - simplified format
      items: saleData.items.map(item => ({
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: item.price,
        subtotal: item.total,
        discount: 0 // Add this field as it's required in your backend
      }))
    };

    console.log('Sending sale data to API:', JSON.stringify(formattedData, null, 2));

    // Use a different endpoint that doesn't rely on the Inventory model
    const response = await api.post('/sales/create-simple', formattedData);
    
    console.log('API Response:', response.data);

    if (response.data.errors) {
      throw new Error(Object.values(response.data.errors).flat().join(', '));
    }
    
    return response.data;
  } catch (error: unknown) {
    console.error('Error creating sale:', error);
    throw error;
  }
};

export const getSales = async () => {
  try {
    const response = await api.get('/sales');
    console.log('Sales API Response:', response); // Log the full response

    // Check if the response is successful
    if (response.status === 200) {
      return response.data || []; // Return an empty array if data is undefined
    } else {
      console.error('Failed to fetch sales:', response.status, response.statusText);
      return []; // Return an empty array on error
    }
  } catch (error) {
    console.error('Error fetching sales:', error);
    return []; // Return an empty array on error to prevent app crash
  }
};

export const getExpenses = async () => {
  try {
    const response = await api.get('/expenses');
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching expenses:', error);
    return [];
  }
};

export const createExpense = async (expenseData: {
  title: string;
  amount: number;
  date: string;
  description: string;
  type: string;
  expense_type_id?: number;
}) => {
  try {
    const response = await api.post('/expenses', expenseData);
    
    if (response.data.errors) {
      throw new Error(Object.values(response.data.errors).flat().join(', '));
    }
    
    return response.data;
  } catch (error) {
    if (error instanceof Error) {
      console.error('Error creating expense:', error.message);
      throw error;
    }
    throw new Error('An unknown error occurred');
  }
};

// Add this function to fetch expense types
export const getExpenseTypes = async () => {
  try {
    const response = await api.get('/expense-types');
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching expense types:', error);
    return [];
  }
}; 