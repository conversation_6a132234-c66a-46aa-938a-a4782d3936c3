{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "appmodules", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-8116e2299b315bc8aba3.json", "name": "appmodules", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86", "source": "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}