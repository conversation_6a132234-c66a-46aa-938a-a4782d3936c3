import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Alert, Linking, Platform } from 'react-native';
import { Text, Card, Button, Portal, Modal, IconButton, DataTable, TextInput } from 'react-native-paper';
import { format } from 'date-fns';
import { useSales } from '../contexts/SalesContext';
import { Sale, Expense } from '../contexts/SalesContext';
import { PDFDocument } from 'react-native-pdf-lib';

// Safe imports for native modules
let BluetoothManager: any = null;
try {
  const BluetoothPrinter = require('react-native-bluetooth-escpos-printer');
  BluetoothManager = BluetoothPrinter.BluetoothManager;
} catch (error) {
  console.warn('Bluetooth printer module not available:', error);
}

let Share: any = null;
try {
  Share = require('react-native-share').default;
} catch (error) {
  console.warn('Share module not available:', error);
}

let RNFS: any = null;
try {
  RNFS = require('react-native-fs');
} catch (error) {
  console.warn('RNFS module not available:', error);
}

// Fallback document directory path
const getDocumentPath = () => {
  if (RNFS && RNFS.DocumentDirectoryPath) {
    return RNFS.DocumentDirectoryPath;
  }
  return Platform.OS === 'ios' ? '/tmp' : '/data/data/com.yourapp/files';
};

type Transaction = Sale | Expense;

const ReportScreen = () => {
  const { sales, expenses } = useSales();
  const [dateType, setDateType] = useState<'start' | 'end'>('start');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [showFilters, setShowFilters] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [pdfPath, setPdfPath] = useState<string | null>(null);
  
  // Replace DateTimePicker with custom date picker
  const [showDateModal, setShowDateModal] = useState(false);
  const [dateInput, setDateInput] = useState({
    day: '',
    month: '',
    year: ''
  });

  // Combine and filter transactions
  const getFilteredTransactions = () => {
    const allTransactions: Transaction[] = [
      ...sales.map(sale => ({ ...sale, type: 'sale' })),
      ...expenses.map(expense => ({ ...expense, type: 'expense' }))
    ];

    return allTransactions.filter(transaction => {
      const transactionDate = new Date(transaction.date || (transaction as Sale).created_at);
      return transactionDate >= startDate && transactionDate <= endDate;
    }).sort((a, b) => {
      const dateA = new Date(a.date || (a as Sale).created_at);
      const dateB = new Date(b.date || (b as Sale).created_at);
      return dateB.getTime() - dateA.getTime();
    });
  };

  const showDatePickerModal = (type: 'start' | 'end') => {
    setDateType(type);
    const date = type === 'start' ? startDate : endDate;
    setDateInput({
      day: date.getDate().toString(),
      month: (date.getMonth() + 1).toString(),
      year: date.getFullYear().toString()
    });
    setShowDateModal(true);
  };

  const handleDateConfirm = () => {
    try {
      const day = parseInt(dateInput.day);
      const month = parseInt(dateInput.month) - 1; // JS months are 0-indexed
      const year = parseInt(dateInput.year);
      
      if (isNaN(day) || isNaN(month) || isNaN(year)) {
        throw new Error('Invalid date');
      }
      
      const newDate = new Date(year, month, day);
      
      if (dateType === 'start') {
        setStartDate(newDate);
      } else {
        setEndDate(newDate);
      }
      
      setShowDateModal(false);
    } catch (error) {
      console.error('Invalid date:', error);
    }
  };

  const calculateTotals = (transactions: Transaction[]) => {
    return transactions.reduce(
      (acc, curr) => {
        if (curr.type === 'sale') {
          acc.sales += parseFloat((curr as Sale).total?.toString() || '0');
        } else {
          acc.expenses += parseFloat((curr as Expense).amount?.toString() || '0');
        }
        return acc;
      },
      { sales: 0, expenses: 0 }
    );
  };

  const printReport = async (transactions: Transaction[]) => {
    try {
      // Check if BluetoothManager is available
      if (!BluetoothManager) {
        console.error('BluetoothManager is not available');
        Alert.alert(
          'Printing Not Available',
          'Bluetooth printing is not available on this device.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Check if bluetooth is enabled
      try {
        const enabled = await BluetoothManager.isBluetoothEnabled();
        if (!enabled) {
          alert('Please enable Bluetooth to print');
          await BluetoothManager.enableBluetooth();
        }
      } catch (error) {
        console.error('Error checking Bluetooth status:', error);
        alert('Unable to access Bluetooth. Please check your device settings.');
        return;
      }

      // Get paired devices
      try {
        const devices = await BluetoothManager.scanDevices();
        console.log('Available devices:', devices);
        
        if (!devices || devices.length === 0) {
          alert('No Bluetooth printers found. Please pair a printer first.');
          return;
        }
        
        // TODO: Let user select printer from devices list
        // For now, use the first available device
        // await BluetoothManager.connect(devices[0].address);
      } catch (error) {
        console.error('Error scanning devices:', error);
        alert('Unable to scan for Bluetooth devices');
        return;
      }

      // Print header
      try {
        await BluetoothManager.printerAlign(BluetoothManager.ALIGN.CENTER);
        await BluetoothManager.printText("Transaction Report\n\n");
        await BluetoothManager.printerAlign(BluetoothManager.ALIGN.LEFT);
        await BluetoothManager.printText(`From: ${format(startDate, 'dd/MM/yyyy')}\n`);
        await BluetoothManager.printText(`To: ${format(endDate, 'dd/MM/yyyy')}\n\n`);

        // Print transactions
        for (const transaction of transactions) {
          const date = format(
            new Date(transaction.date || (transaction as Sale).created_at),
            'dd/MM/yyyy HH:mm'
          );
          
          if (transaction.type === 'sale') {
            const sale = transaction as Sale;
            await BluetoothManager.printText(`Sale #${sale.id} - ${date}\n`);
            await BluetoothManager.printText(`Amount: ৳${parseFloat(sale.total?.toString() || '0').toFixed(2)}\n`);
          } else {
            const expense = transaction as Expense;
            await BluetoothManager.printText(`Expense - ${expense.title} - ${date}\n`);
            await BluetoothManager.printText(`Amount: ৳${parseFloat(expense.amount?.toString() || '0').toFixed(2)}\n`);
          }
          await BluetoothManager.printText("--------------------------------\n");
        }

        // Print totals
        const totals = calculateTotals(transactions);
        await BluetoothManager.printText(`\nTotal Sales: ৳${totals.sales.toFixed(2)}\n`);
        await BluetoothManager.printText(`Total Expenses: ৳${totals.expenses.toFixed(2)}\n`);
        await BluetoothManager.printText(`Net: ৳${(totals.sales - totals.expenses).toFixed(2)}\n`);

        // Cut paper
        await BluetoothManager.printText("\n\n\n");
        
        alert('Printing completed successfully');
      } catch (error) {
        console.error('Error during printing:', error);
        alert('Error during printing. Please try again.');
      }
    } catch (error) {
      console.error('Printing error:', error);
      Alert.alert(
        'Printing Error',
        'An error occurred while trying to print.',
        [{ text: 'OK' }]
      );
    }
  };

  const generatePDF = async (transactions: Transaction[]) => {
    try {
      console.log('Starting PDF generation...');
      
      if (!RNFS) {
        Alert.alert('Error', 'File system access is not available on this device');
        return;
      }
      
      // Create a new PDF document
      const pdfDoc = PDFDocument.create();
      
      // Add header
      pdfDoc.drawText('Transaction Report', {
        x: 250,
        y: 800,
        fontSize: 20,
        color: '#000000',
      });

      // Add date range
      pdfDoc.drawText(`From: ${format(startDate, 'dd/MM/yyyy')}`, {
        x: 50,
        y: 750,
      });
      pdfDoc.drawText(`To: ${format(endDate, 'dd/MM/yyyy')}`, {
        x: 50,
        y: 730,
      });

      // Add transactions
      let yPosition = 680;
      transactions.forEach((transaction) => {
        const date = format(
          new Date(transaction.date || (transaction as Sale).created_at),
          'dd/MM/yyyy HH:mm'
        );
        
        if (transaction.type === 'sale') {
          const sale = transaction as Sale;
          pdfDoc.drawText(`Sale #${sale.id} - ${date}`, { x: 50, y: yPosition });
          pdfDoc.drawText(`৳${parseFloat(sale.total?.toString() || '0').toFixed(2)}`, { x: 400, y: yPosition });
        } else {
          const expense = transaction as Expense;
          pdfDoc.drawText(`${expense.title} - ${date}`, { x: 50, y: yPosition });
          pdfDoc.drawText(`৳${parseFloat(expense.amount?.toString() || '0').toFixed(2)}`, { x: 400, y: yPosition });
        }
        yPosition -= 20;
      });

      // Add totals
      const totals = calculateTotals(transactions);
      yPosition -= 40;
      pdfDoc.drawText(`Total Sales: ৳${totals.sales.toFixed(2)}`, { x: 50, y: yPosition });
      yPosition -= 20;
      pdfDoc.drawText(`Total Expenses: ৳${totals.expenses.toFixed(2)}`, { x: 50, y: yPosition });
      yPosition -= 20;
      pdfDoc.drawText(`Net: ৳${(totals.sales - totals.expenses).toFixed(2)}`, { x: 50, y: yPosition });

      // Save PDF
      const fileName = `report_${format(new Date(), 'yyyyMMdd_HHmmss')}.pdf`;
      const path = `${getDocumentPath()}/${fileName}`;
      
      console.log('Saving PDF to:', path);
      await pdfDoc.save(path);
      console.log('PDF saved successfully');
      
      setPdfPath(path);
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Error generating PDF:', error);
      Alert.alert('Error', 'Failed to generate PDF report');
    }
  };

  const handleShare = async () => {
    if (!pdfPath) {
      Alert.alert('Error', 'No PDF file to share');
      return;
    }

    try {
      if (Share) {
        // Use react-native-share if available
        await Share.open({
          url: `file://${pdfPath}`,
          type: 'application/pdf',
        });
      } else {
        // Fallback to just opening the file
        Alert.alert(
          'Share Not Available',
          'The sharing functionality is not available. Would you like to open the file instead?',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Open File', 
              onPress: () => {
                Linking.openURL(`file://${pdfPath}`).catch(err => {
                  console.error('Error opening file:', err);
                  Alert.alert('Error', 'Could not open the file');
                });
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error sharing file:', error);
      Alert.alert('Error', 'Failed to share the file');
    }
  };

  const filteredTransactions = getFilteredTransactions();
  const totals = calculateTotals(filteredTransactions);

  return (
    <View style={styles.container}>
      {/* Filter Section */}
      <Card style={styles.filterCard}>
        <Card.Content>
          <View style={styles.filterHeader}>
            <Text style={styles.filterTitle}>ফিল্টার</Text>
            <IconButton 
              icon={showFilters ? "chevron-up" : "chevron-down"} 
              onPress={() => setShowFilters(!showFilters)} 
            />
          </View>
          
          {showFilters && (
            <View style={styles.filterContent}>
              <View style={styles.dateRow}>
                <Button 
                  mode="outlined" 
                  onPress={() => showDatePickerModal('start')}
                  style={styles.dateButton}
                >
                  {format(startDate, 'dd/MM/yyyy')}
                </Button>
                <Text>থেকে</Text>
                <Button 
                  mode="outlined" 
                  onPress={() => showDatePickerModal('end')}
                  style={styles.dateButton}
                >
                  {format(endDate, 'dd/MM/yyyy')}
                </Button>
              </View>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Summary Card */}
      <Card style={styles.summaryCard}>
        <Card.Content>
          <Text style={styles.summaryTitle}>সারসংক্ষেপ</Text>
          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>মোট বিক্রয়</Text>
              <Text style={[styles.summaryAmount, { color: '#4CAF50' }]}>
                ৳ {totals.sales.toFixed(2)}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>মোট খরচ</Text>
              <Text style={[styles.summaryAmount, { color: '#F44336' }]}>
                ৳ {totals.expenses.toFixed(2)}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>নেট</Text>
              <Text style={styles.summaryAmount}>
                ৳ {(totals.sales - totals.expenses).toFixed(2)}
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Transactions Table */}
      <DataTable style={styles.table}>
        <DataTable.Header>
          <DataTable.Title>তারিখ</DataTable.Title>
          <DataTable.Title>বিবরণ</DataTable.Title>
          <DataTable.Title numeric>পরিমাণ</DataTable.Title>
        </DataTable.Header>

        <ScrollView>
          {filteredTransactions.map((transaction, index) => (
            <DataTable.Row key={`${transaction.type}-${transaction.id}-${index}`}>
              <DataTable.Cell>
                {format(
                  new Date(transaction.date || (transaction as Sale).created_at),
                  'dd/MM/yyyy'
                )}
              </DataTable.Cell>
              <DataTable.Cell>
                {transaction.type === 'sale' 
                  ? `বিক্রয় #${transaction.id}`
                  : (transaction as Expense).title
                }
              </DataTable.Cell>
              <DataTable.Cell numeric style={{
                color: transaction.type === 'sale' ? '#4CAF50' : '#F44336'
              }}>
                ৳ {transaction.type === 'sale' 
                  ? parseFloat((transaction as Sale).total?.toString() || '0').toFixed(2)
                  : parseFloat((transaction as Expense).amount?.toString() || '0').toFixed(2)
                }
              </DataTable.Cell>
            </DataTable.Row>
          ))}
        </ScrollView>
      </DataTable>

      {/* Print Button */}
      <Button 
        mode="contained"
        icon="printer"
        onPress={() => printReport(filteredTransactions)}
        style={styles.printButton}
      >
        প্রিন্ট রিপোর্ট
      </Button>

      {/* Download Button */}
      <Button 
        mode="contained"
        icon="download"
        onPress={() => {
          console.log('Download button pressed');
          generatePDF(filteredTransactions);
        }}
        style={styles.downloadButton}
      >
        ডাউনলোড রিপোর্ট
      </Button>

      {/* Replace DateTimePicker with custom date picker modal */}
      <Portal>
        <Modal
          visible={showDateModal}
          onDismiss={() => setShowDateModal(false)}
          contentContainerStyle={styles.dateModal}
        >
          <Text style={styles.dateModalTitle}>
            Select {dateType === 'start' ? 'Start' : 'End'} Date
          </Text>
          
          <View style={styles.dateInputContainer}>
            <TextInput
              label="Day"
              value={dateInput.day}
              onChangeText={text => setDateInput({...dateInput, day: text})}
              keyboardType="number-pad"
              style={styles.dateInputField}
              maxLength={2}
            />
            <Text style={styles.dateInputSeparator}>/</Text>
            <TextInput
              label="Month"
              value={dateInput.month}
              onChangeText={text => setDateInput({...dateInput, month: text})}
              keyboardType="number-pad"
              style={styles.dateInputField}
              maxLength={2}
            />
            <Text style={styles.dateInputSeparator}>/</Text>
            <TextInput
              label="Year"
              value={dateInput.year}
              onChangeText={text => setDateInput({...dateInput, year: text})}
              keyboardType="number-pad"
              style={styles.dateInputField}
              maxLength={4}
            />
          </View>
          
          <View style={styles.modalButtons}>
            <Button onPress={() => setShowDateModal(false)}>Cancel</Button>
            <Button mode="contained" onPress={handleDateConfirm}>Confirm</Button>
          </View>
        </Modal>
      </Portal>

      {/* Success Modal */}
      <Portal>
        <Modal
          visible={showSuccessModal}
          onDismiss={() => setShowSuccessModal(false)}
          contentContainerStyle={styles.successModal}
        >
          <IconButton
            icon="check-circle"
            size={48}
            iconColor="#4CAF50"
            style={styles.successIcon}
          />
          <Text style={styles.successText}>Report Downloaded Successfully!</Text>
          <View style={styles.modalButtons}>
            <Button
              mode="outlined"
              onPress={() => setShowSuccessModal(false)}
              style={styles.modalButton}
            >
              Back to reports
            </Button>
            <Button
              mode="contained"
              onPress={handleShare}
              style={styles.modalButton}
            >
              Share reports
            </Button>
          </View>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  filterCard: {
    marginBottom: 16,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  filterContent: {
    marginTop: 8,
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dateButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  summaryCard: {
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#666',
  },
  summaryAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  table: {
    backgroundColor: 'white',
    flex: 1,
    marginBottom: 16,
  },
  printButton: {
    marginTop: 16,
  },
  successModal: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  successIcon: {
    marginBottom: 16,
  },
  successText: {
    fontSize: 18,
    marginBottom: 24,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  downloadButton: {
    marginTop: 16,
  },
  dateModal: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  dateModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  dateInputField: {
    width: 60,
    textAlign: 'center',
    backgroundColor: 'transparent',
  },
  dateInputSeparator: {
    fontSize: 24,
    marginHorizontal: 4,
  },
});

export default ReportScreen; 