import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Appbar, Searchbar, Text, Button, Card, IconButton, Portal, Modal } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

type Item = {
  skuId: string;
  name: string;
  price: number;
  quantity: number;
};

const CreateBillScreen = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [items, setItems] = useState<Item[]>([
    { skuId: '6', name: 'brush', price: 100, quantity: 1 },
    { skuId: '7', name: 't shirt', price: 150, quantity: 1 },
    { skuId: '202', name: 'Mobile', price: 10000, quantity: 1 },
    { skuId: '0', name: 'watch', price: 500, quantity: 1 },
  ]);
  const [showExtraCharges, setShowExtraCharges] = useState(false);

  const total = items.reduce((sum, item) => sum + item.price * item.quantity, 0);

  const updateQuantity = (skuId: string, increment: boolean) => {
    setItems(items.map(item => 
      item.skuId === skuId 
        ? { ...item, quantity: increment ? item.quantity + 1 : Math.max(1, item.quantity - 1) }
        : item
    ));
  };

  return (
    <SafeAreaView style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title="Create Bill" />
        <Appbar.Action icon="dots-vertical" onPress={() => {}} />
      </Appbar.Header>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search by name or SKU ID"
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchBar}
        />
        <IconButton icon="filter-variant" />
        <IconButton icon="dots-vertical" />
      </View>

      <View style={styles.header}>
        <Text>SKU ID</Text>
        <Text>Product Name</Text>
        <Text>Price</Text>
        <Text>Quantity</Text>
      </View>

      <ScrollView style={styles.itemList}>
        {items.map((item) => (
          <Card key={item.skuId} style={styles.itemCard}>
            <Card.Content style={styles.itemContent}>
              <View style={styles.skuContainer}>
                <Text>{item.skuId}</Text>
              </View>
              <Text style={styles.productName}>{item.name}</Text>
              <Text>₹{item.price}</Text>
              <View style={styles.quantityControl}>
                <IconButton 
                  icon="minus" 
                  size={16} 
                  onPress={() => updateQuantity(item.skuId, false)}
                />
                <Text>{item.quantity}</Text>
                <IconButton 
                  icon="plus" 
                  size={16} 
                  onPress={() => updateQuantity(item.skuId, true)}
                />
              </View>
            </Card.Content>
          </Card>
        ))}
      </ScrollView>

      <View style={styles.footer}>
        <Button 
          mode="outlined" 
          onPress={() => setShowExtraCharges(true)}
          style={styles.extraChargesButton}
        >
          Add Extra Charges
        </Button>
        <Text style={styles.total}>₹{total}</Text>
        <Button 
          mode="contained" 
          onPress={() => {}}
          style={styles.summaryButton}
        >
          Bill Summary →
        </Button>
      </View>

      <Portal>
        <Modal
          visible={showExtraCharges}
          onDismiss={() => setShowExtraCharges(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Text style={styles.modalTitle}>No extra charges added yet</Text>
          <Text style={styles.modalSubtitle}>Add suggested charges</Text>
          <View style={styles.chargeButtons}>
            <Button mode="outlined" style={styles.chargeButton}>
              Packaging Charges
            </Button>
            <Button mode="outlined" style={styles.chargeButton}>
              Delivery Charges
            </Button>
          </View>
          <View style={styles.modalActions}>
            <Button onPress={() => setShowExtraCharges(false)}>Cancel</Button>
            <Button mode="contained">Add other charges</Button>
          </View>
        </Modal>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  searchBar: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#fff',
  },
  itemList: {
    flex: 1,
  },
  itemCard: {
    marginHorizontal: 8,
    marginVertical: 4,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  skuContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 4,
    width: 50,
    alignItems: 'center',
  },
  productName: {
    flex: 1,
    marginHorizontal: 8,
  },
  quantityControl: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#ddd',
  },
  extraChargesButton: {
    marginBottom: 8,
  },
  total: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  summaryButton: {
    backgroundColor: '#000',
  },
  modalContainer: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  modalSubtitle: {
    color: '#666',
    marginBottom: 16,
  },
  chargeButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  chargeButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
});

export default CreateBillScreen;