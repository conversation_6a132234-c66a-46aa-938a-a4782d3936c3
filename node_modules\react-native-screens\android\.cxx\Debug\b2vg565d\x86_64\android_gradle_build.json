{"buildFiles": ["C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\b2vg565d\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\b2vg565d\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\b2vg565d\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\b2vg565d\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\b2vg565d\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\b2vg565d\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"rnscreens::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "rnscreens", "output": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\b2vg565d\\obj\\x86_64\\librnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c52e289df292dc4688ccb75a4fa9a38\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}