ninja: Entering directory `C:\xampp\htdocs\calc - Copy\android\app\.cxx\Debug\39285h5l\x86'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86
[0/2] Re-checking globbed directories...
[1/78] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o
[2/78] Building CXX object rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/EventEmitters.cpp.o
[3/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o
[4/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o
[5/78] Building CXX object rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/rnpdf-generated.cpp.o
[6/78] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o
[7/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o
[8/78] Building CXX object rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ShadowNodes.cpp.o
[9/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/d64ea38c7c30dfccf227de60cbb19a8e/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o
[10/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
[11/78] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[12/78] Building CXX object rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ComponentDescriptors.cpp.o
[13/78] Building CXX object rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/Props.cpp.o
[14/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o
[15/78] Building CXX object rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/States.cpp.o
[16/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o
[17/78] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o
[18/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/d739d6a4701d6883befdd9b4196c7a0f/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o
[19/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/edaf542ac2a7885cece174f927cd15fb/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o
[20/78] Building CXX object rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/rnpdfJSI-generated.cpp.o
[21/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1d5113403dcc8894a81ba594471e0811/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[22/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/edaf542ac2a7885cece174f927cd15fb/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o
[23/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3e3c6d7f8f279ad08c790ceb1421407f/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o
[24/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3b1040b81bd757e547469aa9191ff677/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o
[25/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/d739d6a4701d6883befdd9b4196c7a0f/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o
[26/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3eda5b927c96458c5786a241a9383662/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[27/78] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/95b84e780ce91bb7824155d187518eb1/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o
[28/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ef3d49e75505d7d5185884b5dd9e2c0a/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[29/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/99600ba5d0b87b68bcb47f335523a78f/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[30/78] Linking CXX shared library "C:\xampp\htdocs\calc - Copy\android\app\build\intermediates\cxx\Debug\39285h5l\obj\x86\libreact_codegen_safeareacontext.so"
[31/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3eda5b927c96458c5786a241a9383662/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[32/78] Building CXX object CMakeFiles/appmodules.dir/C_/xampp/htdocs/calc_-_Copy/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[33/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0be6a391f3d9db2b1b818023949c88b1/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[34/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/39cd0c497fef929e9fe254e9c18b1b5e/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[35/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3eda5b927c96458c5786a241a9383662/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[36/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3eda5b927c96458c5786a241a9383662/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[37/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0be6a391f3d9db2b1b818023949c88b1/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[38/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3eda5b927c96458c5786a241a9383662/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[39/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[40/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3eda5b927c96458c5786a241a9383662/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[41/78] Building CXX object RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/react/renderer/components/RNShareSpec/EventEmitters.cpp.o
[42/78] Building CXX object RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/react/renderer/components/RNShareSpec/Props.cpp.o
[43/78] Building CXX object RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/RNShareSpec-generated.cpp.o
[44/78] Building CXX object RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/react/renderer/components/RNShareSpec/States.cpp.o
[45/78] Building CXX object RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/react/renderer/components/RNShareSpec/ShadowNodes.cpp.o
[46/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ef3d49e75505d7d5185884b5dd9e2c0a/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[47/78] Building CXX object RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/react/renderer/components/RNShareSpec/RNShareSpecJSI-generated.cpp.o
[48/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/99600ba5d0b87b68bcb47f335523a78f/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[49/78] Building CXX object RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/react/renderer/components/RNShareSpec/ComponentDescriptors.cpp.o
[50/78] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/39cd0c497fef929e9fe254e9c18b1b5e/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
[51/78] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o
[52/78] Linking CXX shared library "C:\xampp\htdocs\calc - Copy\android\app\build\intermediates\cxx\Debug\39285h5l\obj\x86\libreact_codegen_rnscreens.so"
[53/78] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o
[54/78] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o
[55/78] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o
[56/78] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o
[57/78] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o
[58/78] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o
[59/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[60/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[61/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[62/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[63/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[64/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[65/78] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[66/78] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o
[67/78] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp.o
[68/78] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o
[69/78] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o
[70/78] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ComponentDescriptors.cpp.o
[71/78] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o
[72/78] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o
[73/78] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o
[74/78] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp.o
[75/78] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o
[76/78] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o
[77/78] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o
[78/78] Linking CXX shared library "C:\xampp\htdocs\calc - Copy\android\app\build\intermediates\cxx\Debug\39285h5l\obj\x86\libappmodules.so"
