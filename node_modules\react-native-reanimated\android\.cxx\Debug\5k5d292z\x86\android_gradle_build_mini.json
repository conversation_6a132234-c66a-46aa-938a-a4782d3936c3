{"buildFiles": ["C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\5k5d292z\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\5k5d292z\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\5k5d292z\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\hermes-engine\\hermes-engineConfig.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\5k5d292z\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\hermes-engine\\hermes-engineConfigVersion.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\5k5d292z\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\5k5d292z\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\CMakeLists.txt", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\5k5d292z\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\5k5d292z\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"worklets::@a0394df2d94e5212d8bd": {"artifactName": "worklets", "abi": "x86", "output": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\5k5d292z\\obj\\x86\\libworklets.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c52e289df292dc4688ccb75a4fa9a38\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1cbc471af91dd7cd77a14280d219e183\\transformed\\hermes-android-0.79.5-debug\\prefab\\modules\\libhermes\\libs\\android.x86\\libhermes.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\hermestooling\\libs\\android.x86\\libhermestooling.so"]}, "reanimated::@89a6a9b85fb42923616c": {"artifactName": "reanimated", "abi": "x86", "output": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\5k5d292z\\obj\\x86\\libreanimated.so", "runtimeFiles": ["C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\5k5d292z\\obj\\x86\\libworklets.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c52e289df292dc4688ccb75a4fa9a38\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1cbc471af91dd7cd77a14280d219e183\\transformed\\hermes-android-0.79.5-debug\\prefab\\modules\\libhermes\\libs\\android.x86\\libhermes.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\hermestooling\\libs\\android.x86\\libhermestooling.so"]}}}