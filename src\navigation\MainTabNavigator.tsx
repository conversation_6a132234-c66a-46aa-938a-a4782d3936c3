import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { IconButton } from 'react-native-paper';
import DashboardScreen from '../screens/DashboardScreen';
// import ReportScreen from '../screens/ReportScreen'; // Commented out since module not found
import BillingScreen from '../screens/BillingScreen';
import ReportScreen from '../screens/ReportScreen';
//import ReportsScreen from '../screens/ReportScreen';
import CreateBillScreen from '../screens/CreateBillScreen';
import InventoryScreen from '../screens/InventoryScreen';

const Tab = createMaterialTopTabNavigator();
const screenWidth = Dimensions.get('window').width;

const Header = () => (
  <View style={styles.header}>
    
    <View style={styles.headerContent}>
      <Text style={styles.logo}>Hi, Maruf</Text>
      <View style={styles.headerIcons}>
        <IconButton 
          icon="calculator" 
          size={24} 
          iconColor="#666"
          onPress={() => {}}
        />
        <IconButton 
          icon="help-circle-outline" 
          size={24} 
          iconColor="#666"
          onPress={() => {}}
        />
        <IconButton 
          icon="cog" 
          size={24} 
          iconColor="#666"
          onPress={() => {}}
        />
      </View>
    </View>
  </View>
);

const MainTabNavigator = () => {
  return (
    <View style={styles.container}>
      <Header />
      <Tab.Navigator
        screenOptions={{
          tabBarLabelStyle: styles.tabLabel,
          tabBarStyle: styles.tabBar,
          tabBarIndicatorStyle: styles.tabIndicator,
          tabBarScrollEnabled: true,
          tabBarItemStyle: styles.tabItem,
          tabBarGap: 0,
        }}
      >
        <Tab.Screen 
          name="Dashboard" 
          component={DashboardScreen}
          options={{ tabBarLabel: 'Dashboard' }}
        />
        <Tab.Screen 
          name="Inventory" 
          component={InventoryScreen}
          options={{ tabBarLabel: 'Inventory' }}
        />
        <Tab.Screen 
          name="Reports" 
          component={ReportScreen}
          options={{ tabBarLabel: 'Reports' }}
        />
        <Tab.Screen 
          name="Billing" 
          component={BillingScreen}
          options={{ tabBarLabel: 'Billing' }}
        />
       
      </Tab.Navigator>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: '#fff',
    paddingTop: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginTop: 20,
  },
  logo: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tabBar: {
    backgroundColor: '#fff',
    elevation: 0,
    shadowOpacity: 0,
    height: 48,
  },
  tabLabel: {
    textTransform: 'none',
    fontSize: 14,
    fontWeight: '500',
    width: 'auto',
    marginHorizontal: 0,
  },
  tabIndicator: {
    backgroundColor: '#000',
    
  },
  tabItem: {
    width: screenWidth / 3.5, // Show about 3.5 tabs at once
    padding: 0,
    minHeight: 48,
  },
});

export default MainTabNavigator; 