{"artifacts": [{"path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/5k5d292z/obj/x86/libworklets.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_options", "target_include_directories"], "files": ["src/main/cpp/worklets/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 16, "parent": 0}, {"command": 1, "file": 0, "line": 49, "parent": 0}, {"command": 1, "file": 0, "line": 52, "parent": 0}, {"command": 1, "file": 0, "line": 60, "parent": 0}, {"command": 1, "file": 0, "line": 64, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 12, "parent": 6}, {"command": 3, "file": 0, "line": 20, "parent": 0}, {"command": 3, "file": 0, "line": 23, "parent": 0}, {"command": 3, "file": 0, "line": 32, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC"}, {"backtrace": 7, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 7, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 7, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 7, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 7, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 7, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 7, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 7, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}, {"fragment": "-std=gnu++20"}], "defines": [{"define": "worklets_EXPORTS"}], "includes": [{"backtrace": 8, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp"}, {"backtrace": 8, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp"}, {"backtrace": 9, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon"}, {"backtrace": 9, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule"}, {"backtrace": 9, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon"}, {"backtrace": 9, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker"}, {"backtrace": 9, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor"}, {"backtrace": 10, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga"}, {"backtrace": 10, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "worklets::@a0394df2d94e5212d8bd", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "-llog", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c52e289df292dc4688ccb75a4fa9a38\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1cbc471af91dd7cd77a14280d219e183\\transformed\\hermes-android-0.79.5-debug\\prefab\\modules\\libhermes\\libs\\android.x86\\libhermes.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\hermestooling\\libs\\android.x86\\libhermestooling.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "worklets", "nameOnDisk": "libworklets.so", "paths": {"build": "src/main/cpp/worklets", "source": "src/main/cpp/worklets"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/worklets/android/AndroidUIScheduler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/worklets/android/PlatformLogger.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/worklets/android/WorkletsModule.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/worklets/android/WorkletsOnLoad.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}