import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, FlatList } from 'react-native';
import { Text, Searchbar, Card, Button, TextInput, Portal, Modal, IconButton, DataTable } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { getProducts, createSale } from '../services/api';
import { useSales } from '../contexts/SalesContext';
import { NavigationProp, ParamListBase } from '@react-navigation/native';

// Define the Product type
interface Product {
  id: number;
  name: string;
  sku: string;
  price: number;
  stock?: number;
  description?: string;
}

type SaleItem = {
  id: number;
  product_id: number;
  product_name: string;
  sku: string;
  quantity: number;
  price: number;
  total: number;
};

interface SalesScreenProps {
  navigation: NavigationProp<ParamListBase>;
}

const SalesScreen = ({ navigation }: SalesScreenProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<SaleItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentDetails, setPaymentDetails] = useState({
    subtotal: 0,
    discount: '',
    tax: '',
    total: 0,
    receivedAmount: '',
    changeAmount: 0,
  });
  const { addSale } = useSales();
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const data = await getProducts();
      setProducts(data);
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = (product: Product) => {
    const existingItem = selectedProducts.find(item => item.product_id === product.id);
    
    if (existingItem) {
      setSelectedProducts(selectedProducts.map(item => 
        item.product_id === product.id 
          ? { ...item, quantity: item.quantity + 1, total: (item.quantity + 1) * item.price }
          : item
      ));
    } else {
      setSelectedProducts([...selectedProducts, {
        id: Date.now(),
        product_id: product.id,
        product_name: product.name,
        sku: product.sku,
        quantity: 1,
        price: product.price,
        total: product.price
      }]);
    }
  };

  const updateQuantity = (itemId: number, increment: boolean) => {
    setSelectedProducts(selectedProducts.map(item => {
      if (item.id === itemId) {
        const newQuantity = increment ? item.quantity + 1 : Math.max(1, item.quantity - 1);
        return {
          ...item,
          quantity: newQuantity,
          total: newQuantity * item.price
        };
      }
      return item;
    }));
  };

  const removeItem = (itemId: number) => {
    setSelectedProducts(selectedProducts.filter(item => item.id !== itemId));
  };

  const calculateTotal = () => {
    const subtotal = selectedProducts.reduce((sum, item) => sum + item.total, 0);
    const discount = parseFloat(paymentDetails.discount) || 0;
    const tax = parseFloat(paymentDetails.tax) || 0;
    const total = subtotal - discount + tax;
    
    setPaymentDetails({
      ...paymentDetails,
      subtotal,
      total,
      changeAmount: parseFloat(paymentDetails.receivedAmount) - total || 0
    });
  };

  useEffect(() => {
    calculateTotal();
  }, [selectedProducts, paymentDetails.discount, paymentDetails.tax, paymentDetails.receivedAmount]);

  const handleCompleteSale = async () => {
    try {
      // Format the items properly for the API
      const formattedItems = selectedProducts.map(item => ({
        product_id: item.product_id,
        quantity: item.quantity,
        price: item.price,
        total: item.total,
        // Add any other fields required by your API
        unit_price: item.price,
        sub_total: item.total,
        discount: 0,
        tax: 0
      }));

      const saleData = {
        total: paymentDetails.total,
        tax: parseFloat(paymentDetails.tax) || 0,
        discount: parseFloat(paymentDetails.discount) || 0,
        items: formattedItems,
        // Add these fields that might be required by your API
        payment_status: 'paid',
        status: 'completed',
        date: new Date().toISOString(),
        customer_id: 1, // Default customer ID
        payment_method: 'cash'
      };

      console.log('Sending sale data to API:', JSON.stringify(saleData, null, 2)); // Detailed debug log

      // First create sale in backend
      const response = await createSale(saleData);
      console.log('API response:', response); // Log the API response

      // Then update local state
      const sale = {
        id: response.id || Date.now(),
        items: selectedProducts,
        total: paymentDetails.total,
        date: new Date(),
        paymentMethod: 'নগদ',
        payment_status: 'paid',
        status: 'completed',
        created_at: new Date().toISOString()
      };

      addSale(sale);
      setShowPaymentModal(false);
      setSelectedProducts([]);
      resetPaymentDetails();
      
      // Show success message
      Alert.alert('Success', 'Sale completed successfully!');
      
      navigation.goBack();
    } catch (error) {
      console.error('Error completing sale:', error);
      Alert.alert('Error', 'Failed to complete sale. Please try again.');
    }
  };

  const resetPaymentDetails = () => {
    setPaymentDetails({
      subtotal: 0,
      discount: '',
      tax: '',
      total: 0,
      receivedAmount: '',
      changeAmount: 0,
    });
  };

  const renderItem = ({ item }: { item: Product }) => (
    <Card style={styles.productCard}>
      <Card.Content style={styles.productContent}>
        <View style={styles.skuContainer}>
          <Text style={styles.skuText}>{item.sku || 'N/A'}</Text>
        </View>
        <View style={styles.productDetails}>
          <Text style={styles.productName}>{item.name}</Text>
          <Text style={styles.productPrice}>৳{item.price}</Text>
        </View>
        <Button 
          mode="contained" 
          onPress={() => {
            addToCart(item);
            setIsSearchFocused(false);
          }}
          style={styles.addButton}
          labelStyle={styles.addButtonLabel}
        >
          Add +
        </Button>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Search Section */}
        <View style={styles.searchSection}>
          <Searchbar
            placeholder="Search by name or SKU ID"
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchBar}
             onFocus={() => setIsSearchFocused(true)}
          />
          <IconButton 
            icon="filter-variant" 
            size={24}
            onPress={() => {}}
          />
          <IconButton 
            icon="information" 
            size={24}
            onPress={() => {}}
          />
        </View>

        {/* Products List */}
        <FlatList
          data={products.filter(product =>
            product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (product.sku && product.sku.toLowerCase().includes(searchQuery.toLowerCase()))
          )}
          renderItem={renderItem}
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={styles.listContainer}
        />

       
      </View>
 {/* Quick Item Add Button */}
 <Button
          mode="contained"
          onPress={() => {}}
          style={styles.quickAddButton}
          icon="plus"
        >
          Quick Item Add
        </Button>
      {/* Right Side - Cart */}
      <View style={styles.cartSection}>
        <Text style={styles.cartTitle}>বর্তমান বিক্রয়</Text>
        
        <ScrollView style={styles.cartItems}>
          <DataTable>
            <DataTable.Header style={styles.cartHeader}>
              <DataTable.Title>SKU</DataTable.Title>
              <DataTable.Title>পণ্য</DataTable.Title>
              <DataTable.Title numeric>পরিমাণ</DataTable.Title>
              <DataTable.Title numeric>মূল্য</DataTable.Title>
              <DataTable.Title numeric>মোট</DataTable.Title>
              <DataTable.Title>Actions</DataTable.Title>
            </DataTable.Header>

            {selectedProducts.map(item => (
              <DataTable.Row key={item.id}>
                <DataTable.Cell>{item.sku}</DataTable.Cell>
                <DataTable.Cell>{item.product_name}</DataTable.Cell>
                <DataTable.Cell numeric>
                  <View style={styles.quantityControl}>
                    <IconButton 
                      icon="minus"
                      size={16} 
                      onPress={() => updateQuantity(item.id, false)}
                    />
                    <Text>{item.quantity}</Text>
                    <IconButton 
                      icon="plus"
                      size={16} 
                      onPress={() => updateQuantity(item.id, true)}
                    />
                  </View>
                </DataTable.Cell>
                <DataTable.Cell numeric>{item.price}৳</DataTable.Cell>
                <DataTable.Cell numeric>{item.total}৳</DataTable.Cell>
                <DataTable.Cell>
                  <IconButton 
                    icon="delete" 
                    size={16} 
                    onPress={() => removeItem(item.id)}
                  />
                </DataTable.Cell>
              </DataTable.Row>
            ))}
          </DataTable>
        </ScrollView>

        <View style={styles.cartFooter}>
          <Text style={styles.total}>মোট: ৳{paymentDetails.total}</Text>
          <Button 
            mode="contained" 
            onPress={() => setShowPaymentModal(true)}
            disabled={selectedProducts.length === 0}
          >
            পেমেন্টে এগিয়ে যান
          </Button>
        </View>
      </View>

      {/* Payment Modal */}
      <Portal>
        <Modal
          visible={showPaymentModal}
          onDismiss={() => setShowPaymentModal(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Text style={styles.modalTitle}>পেমেন্টের বিস্তারিত</Text>
          
          <TextInput
            label="সাবটোটাল"
            value={paymentDetails.subtotal.toString()}
            disabled
            style={styles.input}
          />
          
          <TextInput
            label="ছাড়"
            value={paymentDetails.discount}
            onChangeText={text => setPaymentDetails({...paymentDetails, discount: text})}
            keyboardType="numeric"
            style={styles.input}
          />
          
          <TextInput
            label="কর"
            value={paymentDetails.tax}
            onChangeText={text => setPaymentDetails({...paymentDetails, tax: text})}
            keyboardType="numeric"
            style={styles.input}
          />
          
          <TextInput
            label="মোট"
            value={paymentDetails.total.toString()}
            disabled
            style={styles.input}
          />
          
          <TextInput
            label="প্রাপ্ত পরিমাণ"
            value={paymentDetails.receivedAmount}
            onChangeText={text => setPaymentDetails({...paymentDetails, receivedAmount: text})}
            keyboardType="numeric"
            style={styles.input}
          />
          
          <TextInput
            label="বদল"
            value={paymentDetails.changeAmount.toString()}
            disabled
            style={styles.input}
          />

          <View style={styles.modalButtons}>
            <Button onPress={() => setShowPaymentModal(false)}>বাতিল</Button>
            <Button 
              mode="contained" 
              onPress={handleCompleteSale}
              disabled={paymentDetails.changeAmount < 0}
            >
              বিক্রয় সম্পন্ন করুন
            </Button>
          </View>
        </Modal>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  searchSection: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: 'white',
  },
  searchBar: {
    flex: 1,
    marginRight: 8,
    backgroundColor: '#f5f5f5',
  },
  listContainer: {
    padding: 8,
  },
  productCard: {
    marginBottom: 8,
    elevation: 2,
  },
  productContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  skuContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 8,
    marginRight: 12,
    minWidth: 60,
    alignItems: 'center',
  },
  skuText: {
    fontSize: 14,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 14,
    color: '#666',
  },
  addButton: {
    backgroundColor: '#000',
    borderRadius: 4,
  },
  addButtonLabel: {
    fontSize: 12,
  },
  quickAddButton: {
    marginLeft:16,
    marginRight:16,
    backgroundColor: '#4CAF50',
  },
  cartSection: {
    flex: 1,
    backgroundColor: '#fff',
    borderLeftWidth: 1,
    borderLeftColor: '#eee',
    padding: 16,
  },
  cartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  cartItems: {
    flex: 1,
  },
  cartHeader: {
    backgroundColor: '#f5f5f5',
  },
  quantityControl: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cartFooter: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
    gap: 8,
  },
  total: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalContainer: {
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 8,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
  },
});

export default SalesScreen; 