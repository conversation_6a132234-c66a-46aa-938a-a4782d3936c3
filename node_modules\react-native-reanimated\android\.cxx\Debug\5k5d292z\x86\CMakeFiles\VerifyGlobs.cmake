# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# REANIMATED_COMMON_CPP_SOURCES at src/main/cpp/reanimated/CMakeLists.txt:3 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/PropsRegistry.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Tools/FeaturesConfig.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/CMakeFiles/cmake.verify_globs")
endif()

# WORKLETS_COMMON_CPP_SOURCES at src/main/cpp/worklets/CMakeLists.txt:3 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Registries/EventHandlerRegistry.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/SharedItems/Shareables.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/AsyncQueue.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/JSISerializer.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/JSLogger.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/JSScheduler.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/ReanimatedVersion.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/UIScheduler.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/WorkletEventHandler.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/CMakeFiles/cmake.verify_globs")
endif()

# REANIMATED_ANDROID_CPP_SOURCES at src/main/cpp/reanimated/CMakeLists.txt:5 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/JNIHelper.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/LayoutAnimations.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/NativeProxy.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/CMakeFiles/cmake.verify_globs")
endif()

# WORKLETS_ANDROID_CPP_SOURCES at src/main/cpp/worklets/CMakeLists.txt:5 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp/worklets/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/AndroidUIScheduler.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/PlatformLogger.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsModule.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsOnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/CMakeFiles/cmake.verify_globs")
endif()
