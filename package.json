{"dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/cli-server-api": "17.0.0", "@react-native-community/datetimepicker": "^8.3.0", "@react-navigation/material-top-tabs": "^7.3.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "axios": "^1.10.0", "date-fns": "^4.1.0", "expo": "~53.0.17", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-pager-view": "^6.8.1", "react-native-paper": "^5.14.5", "react-native-pdf-lib": "^1.0.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-share": "^12.0.10", "react-native-tab-view": "^4.1.2", "react-native-vector-icons": "10.0.3", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "name": "calc - Copy", "version": "1.0.0"}