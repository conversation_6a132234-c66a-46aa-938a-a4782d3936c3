{"dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/cli-server-api": "^18.0.0", "@react-native-community/datetimepicker": "^8.4.2", "@react-navigation/material-top-tabs": "^6.6.14", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "axios": "^1.10.0", "date-fns": "^4.1.0", "expo": "^53.0.16", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.7", "react-native-bluetooth-escpos-printer": "^0.0.5", "react-native-fs": "^2.20.0", "react-native-pager-view": "^6.8.1", "react-native-paper": "^5.14.5", "react-native-pdf-lib": "^1.0.0", "react-native-reanimated": "~3.18.0", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "~4.11.1", "react-native-share": "^12.1.0", "react-native-tab-view": "^4.1.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0"}, "devDependencies": {"@expo/cli": "^0.24.0", "@react-native-community/cli": "^12.3.5", "@react-native-community/cli-platform-android": "^12.3.5", "@react-native-community/cli-platform-ios": "^12.3.5", "@types/react": "~18.3.12", "metro": "^0.76.7", "metro-core": "^0.76.7", "metro-react-native-babel-preset": "^0.77.0", "typescript": "^5.3.3"}, "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}}