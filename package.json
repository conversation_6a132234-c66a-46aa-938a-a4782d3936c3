{"dependencies": {"@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/cli-server-api": "^17.0.0", "@react-native-community/datetimepicker": "^8.3.0", "@react-navigation/material-top-tabs": "^6.6.5", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.12", "@types/react-native": "^0.73.0", "axios": "^1.8.2", "date-fns": "^4.1.0", "expo": "^52.0.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.7", "react-native-bluetooth-escpos-printer": "^0.0.5", "react-native-fs": "^2.20.0", "react-native-pager-view": "6.5.1", "react-native-paper": "5.10.3", "react-native-pdf-lib": "^1.0.0", "react-native-reanimated": "~3.6.0", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-share": "^12.0.10", "react-native-tab-view": "^3.5.2", "react-native-vector-icons": "10.0.3", "react-native-web": "~0.19.13"}, "devDependencies": {"@react-native-community/cli": "^12.3.5", "@react-native-community/cli-platform-android": "^12.3.5", "@react-native-community/cli-platform-ios": "^12.3.5", "@types/react": "~18.3.12", "expo-cli": "^6.3.12", "metro": "^0.76.7", "metro-core": "^0.76.7", "metro-react-native-babel-preset": "^0.77.0", "typescript": "^5.3.3"}, "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}}