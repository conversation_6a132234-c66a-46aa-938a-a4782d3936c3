[{"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"C:/xampp/htdocs/calc - Copy/android/app/build/generated/autolinking/src/main/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"C:/xampp/htdocs/calc - Copy/android/app/build/generated/autolinking/src/main/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\RNDateTimePickerCGen-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\RNDateTimePickerCGen-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\RNDateTimePickerCGen-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\ComponentDescriptors.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ComponentDescriptors.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ComponentDescriptors.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\EventEmitters.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\EventEmitters.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\EventEmitters.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\Props.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\Props.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\Props.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\RNDateTimePickerCGenJSI-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\RNDateTimePickerCGenJSI-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\RNDateTimePickerCGenJSI-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\ShadowNodes.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ShadowNodes.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ShadowNodes.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\States.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\States.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\States.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\pagerview-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\pagerview-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\pagerview-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\ComponentDescriptors.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\ComponentDescriptors.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\ComponentDescriptors.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\EventEmitters.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\EventEmitters.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\EventEmitters.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\Props.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\Props.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\Props.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\ShadowNodes.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\ShadowNodes.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\ShadowNodes.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\States.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\States.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\States.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\pagerviewJSI-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\pagerviewJSI-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\pagerviewJSI-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpdf_autolinked_build\\CMakeFiles\\react_codegen_rnpdf.dir\\react\\renderer\\components\\rnpdf\\ComponentDescriptors.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpdf\\ComponentDescriptors.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpdf\\ComponentDescriptors.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpdf_autolinked_build\\CMakeFiles\\react_codegen_rnpdf.dir\\react\\renderer\\components\\rnpdf\\EventEmitters.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpdf\\EventEmitters.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpdf\\EventEmitters.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpdf_autolinked_build\\CMakeFiles\\react_codegen_rnpdf.dir\\react\\renderer\\components\\rnpdf\\Props.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpdf\\Props.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpdf\\Props.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpdf_autolinked_build\\CMakeFiles\\react_codegen_rnpdf.dir\\react\\renderer\\components\\rnpdf\\ShadowNodes.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpdf\\ShadowNodes.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpdf\\ShadowNodes.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpdf_autolinked_build\\CMakeFiles\\react_codegen_rnpdf.dir\\react\\renderer\\components\\rnpdf\\States.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpdf\\States.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpdf\\States.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpdf_autolinked_build\\CMakeFiles\\react_codegen_rnpdf.dir\\react\\renderer\\components\\rnpdf\\rnpdfJSI-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpdf\\rnpdfJSI-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnpdf\\rnpdfJSI-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnpdf_autolinked_build\\CMakeFiles\\react_codegen_rnpdf.dir\\rnpdf-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\rnpdf-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\rnpdf-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\Props.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\States.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\rnreanimated-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\4333b6311a40b70ac555888f866bddf0\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\4ca88d5c60c62ab585cfbcc5598b57bf\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3e3c6d7f8f279ad08c790ceb1421407f\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3b1040b81bd757e547469aa9191ff677\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\edaf542ac2a7885cece174f927cd15fb\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3b1040b81bd757e547469aa9191ff677\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3b1040b81bd757e547469aa9191ff677\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\de48d3e080a3e9af351539354bb31c0a\\components\\safeareacontext\\safeareacontextJSI-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\751d46a4e65b06e1fc3c30271eedb570\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\0be6a391f3d9db2b1b818023949c88b1\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\3eda5b927c96458c5786a241a9383662\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\3eda5b927c96458c5786a241a9383662\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\8c0573c1b9c01b24d29233a25df59c09\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\0be6a391f3d9db2b1b818023949c88b1\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\8c0573c1b9c01b24d29233a25df59c09\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\2cc0eb7b30051ea3fe0d1631ac4b672f\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\3eda5b927c96458c5786a241a9383662\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\ad91e85688bd13ae929480151097e972\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\99600ba5d0b87b68bcb47f335523a78f\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\39cd0c497fef929e9fe254e9c18b1b5e\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\99600ba5d0b87b68bcb47f335523a78f\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\39cd0c497fef929e9fe254e9c18b1b5e\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\ad91e85688bd13ae929480151097e972\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNShareSpec_autolinked_build\\CMakeFiles\\react_codegen_RNShareSpec.dir\\RNShareSpec-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\RNShareSpec-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\RNShareSpec-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNShareSpec_autolinked_build\\CMakeFiles\\react_codegen_RNShareSpec.dir\\react\\renderer\\components\\RNShareSpec\\ComponentDescriptors.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNShareSpec\\ComponentDescriptors.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNShareSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNShareSpec_autolinked_build\\CMakeFiles\\react_codegen_RNShareSpec.dir\\react\\renderer\\components\\RNShareSpec\\EventEmitters.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNShareSpec\\EventEmitters.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNShareSpec\\EventEmitters.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNShareSpec_autolinked_build\\CMakeFiles\\react_codegen_RNShareSpec.dir\\react\\renderer\\components\\RNShareSpec\\Props.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNShareSpec\\Props.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNShareSpec\\Props.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNShareSpec_autolinked_build\\CMakeFiles\\react_codegen_RNShareSpec.dir\\react\\renderer\\components\\RNShareSpec\\RNShareSpecJSI-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNShareSpec\\RNShareSpecJSI-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNShareSpec\\RNShareSpecJSI-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNShareSpec_autolinked_build\\CMakeFiles\\react_codegen_RNShareSpec.dir\\react\\renderer\\components\\RNShareSpec\\ShadowNodes.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNShareSpec\\ShadowNodes.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNShareSpec\\ShadowNodes.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNShareSpec_autolinked_build\\CMakeFiles\\react_codegen_RNShareSpec.dir\\react\\renderer\\components\\RNShareSpec\\States.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNShareSpec\\States.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNShareSpec\\States.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\RNVectorIconsSpec-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp"}]