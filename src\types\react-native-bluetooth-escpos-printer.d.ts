declare module 'react-native-bluetooth-escpos-printer' {
  export const BluetoothManager: {
    isBluetoothEnabled(): Promise<boolean>;
    enableBluetooth(): Promise<void>;
    scanDevices(): Promise<any[]>;
    connect(address: string): Promise<void>;
    printerAlign(alignment: number): Promise<void>;
    printText(text: string): Promise<void>;
    ALIGN: {
      CENTER: number;
      LEFT: number;
      RIGHT: number;
    };
    DIRECTION: {
      FORWARD: number;
      BACKWARD: number;
    };
  };
} 