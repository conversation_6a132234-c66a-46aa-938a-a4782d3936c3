{"buildFiles": ["C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1u3b6c5l\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1u3b6c5l\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1u3b6c5l\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1u3b6c5l\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\src\\fabric\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1u3b6c5l\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1u3b6c5l\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"fabric::@3c04bbf757b97f4dae7c": {"artifactName": "fabric", "abi": "x86", "output": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1u3b6c5l\\x86\\src\\fabric\\libfabric.a", "runtimeFiles": []}, "expo-modules-core::@6890427a1f51a3e7e1df": {"artifactName": "expo-modules-core", "abi": "x86", "output": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\expo-modules-core\\android\\build\\intermediates\\cxx\\Debug\\1u3b6c5l\\obj\\x86\\libexpo-modules-core.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c52e289df292dc4688ccb75a4fa9a38\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c52e289df292dc4688ccb75a4fa9a38\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eda7ef8193d39658292351753e97397\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}}}