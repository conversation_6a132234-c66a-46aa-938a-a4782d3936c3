{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-109356adc70b3abe44d3.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "RNDateTimePickerCGen_autolinked_build", "jsonFile": "directory-RNDateTimePickerCGen_autolinked_build-Debug-fb6259a5ba07a4588470.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "pagerview_autolinked_build", "jsonFile": "directory-pagerview_autolinked_build-Debug-5f464c0209fb7aff5b45.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "rnpdf_autolinked_build", "jsonFile": "directory-rnpdf_autolinked_build-Debug-fcfbeba58ffc72b663d7.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-6882c7d0b859a7f64dce.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [7]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-4dc3c27c2961e157b585.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [9]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-83735d64d24fdfeea55b.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [8]}, {"build": "RNShareSpec_autolinked_build", "jsonFile": "directory-RNShareSpec_autolinked_build-Debug-865a6fbff85ecca18317.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-ce8ccc04aa75db10168a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [3]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-c3611cc5cd0233e46c7d.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2", "jsonFile": "target-react_codegen_RNDateTimePickerCGen-Debug-6172ce5fc545f7ba6080.json", "name": "react_codegen_RNDateTimePickerCGen", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_RNShareSpec::@e6c4c2b5aedb2ea213b7", "jsonFile": "target-react_codegen_RNShareSpec-Debug-2a13a2a51e79bbb76f8e.json", "name": "react_codegen_RNShareSpec", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-f7ab3350d685d38779b7.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_pagerview::@7032a8921530ec438d60", "jsonFile": "target-react_codegen_pagerview-Debug-5b4d39ab46df8d19201c.json", "name": "react_codegen_pagerview", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-76f4aac38e676ecddb45.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnpdf::@7ef4ba29a735fbf48be7", "jsonFile": "target-react_codegen_rnpdf-Debug-70e5f520c9b6cbba53f9.json", "name": "react_codegen_rnpdf", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-3b6fe3f122ff55d9efde.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-bf4837323f36306e224f.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-b158ae0fafd986e25ea6.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86", "source": "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}