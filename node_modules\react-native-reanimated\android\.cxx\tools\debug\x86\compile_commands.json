[{"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxySpec.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxySpec.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxySpec.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\4390285b2d5f301878976b97edb28644\\react-native-reanimated\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\4390285b2d5f301878976b97edb28644\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\4390285b2d5f301878976b97edb28644\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\xampp\\htdocs\\calc_-_Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\4390285b2d5f301878976b97edb28644\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\4390285b2d5f301878976b97edb28644\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\AndroidUIScheduler.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\AndroidUIScheduler.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\AndroidUIScheduler.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\PlatformLogger.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\PlatformLogger.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\PlatformLogger.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\WorkletsModule.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsModule.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsModule.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\WorkletsOnLoad.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsOnLoad.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsOnLoad.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\Fabric\\PropsRegistry.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\PropsRegistry.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\PropsRegistry.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxy.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxy.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxy.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxySpec.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxySpec.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxySpec.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\9e5f2046f2042b7d315a3c844fef389e\\Common\\cpp\\reanimated\\Tools\\FeaturesConfig.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Tools\\FeaturesConfig.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Tools\\FeaturesConfig.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\JNIHelper.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\JNIHelper.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\JNIHelper.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\LayoutAnimations.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\LayoutAnimations.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\LayoutAnimations.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\NativeProxy.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\NativeProxy.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\NativeProxy.cpp"}, {"directory": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga\" -I\"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\OnLoad.cpp.o -c \"C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\OnLoad.cpp\"", "file": "C:\\xampp\\htdocs\\calc - Copy\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\OnLoad.cpp"}]