ninja: Entering directory `C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android\.cxx\Debug\5k5d292z\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o
[2/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o
[3/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/4390285b2d5f301878976b97edb28644/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp.o
[4/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Tools/UIScheduler.cpp.o
[5/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Tools/JSISerializer.cpp.o
[6/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/4390285b2d5f301878976b97edb28644/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp.o
[7/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o
[8/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/SharedItems/Shareables.cpp.o
[9/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Tools/JSScheduler.cpp.o
[10/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o
[11/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o
[12/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o
[13/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o
[14/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o
[15/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o
[16/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o
[17/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o
[18/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/030df3b4498cee2081331b6fc1a39ab1/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o
[19/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o
[20/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o
[21/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o
[22/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/030df3b4498cee2081331b6fc1a39ab1/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o
[23/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Tools/FeaturesConfig.cpp.o
[24/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Fabric/PropsRegistry.cpp.o
[25/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o
[26/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o
[27/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/030df3b4498cee2081331b6fc1a39ab1/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o
[28/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o
[29/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o
[30/41] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\arm64-v8a\libworklets.so
[31/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/030df3b4498cee2081331b6fc1a39ab1/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o
[32/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o
[33/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/030df3b4498cee2081331b6fc1a39ab1/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o
[34/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o
[35/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/030df3b4498cee2081331b6fc1a39ab1/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o
[36/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o
[37/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/030df3b4498cee2081331b6fc1a39ab1/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o
[38/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o
[39/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/030df3b4498cee2081331b6fc1a39ab1/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o
[40/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o
[41/41] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\arm64-v8a\libreanimated.so
