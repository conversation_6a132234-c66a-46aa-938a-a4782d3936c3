{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86/CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake"}, {"isGenerated": true, "path": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86/CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86/CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfigVersion.cmake"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfig.cmake"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86", "source": "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 1, "minor": 0}}