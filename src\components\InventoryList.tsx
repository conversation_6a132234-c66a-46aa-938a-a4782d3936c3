import React from 'react';
import { FlatList, View } from 'react-native';
import { List, Searchbar } from 'react-native-paper';
import { InventoryItem } from '../types';

const InventoryList = ({ items }: { items: InventoryItem[] }) => {
  const [searchQuery, setSearchQuery] = React.useState('');

  const filteredItems = items.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <View style={{ flex: 1 }}>
      <Searchbar
        placeholder="Search inventory"
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={{ margin: 8 }}
      />
      
      <FlatList
        data={filteredItems}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <List.Item
            title={item.name}
            description={`Stock: ${item.quantity} | $${item.price}`}
            left={props => <List.Icon {...props} icon="cube" />}
            right={props => (
              <Text style={{ alignSelf: 'center' }}>
                {item.quantity < 10 ? 'Low Stock' : 'In Stock'}
              </Text>
            )}
          />
        )}
      />
    </View>
  );
};

export default InventoryList; 