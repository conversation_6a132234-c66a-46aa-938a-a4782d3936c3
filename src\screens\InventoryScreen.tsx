import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Searchbar, IconButton, Card, Button, Portal, Modal, TextInput, ActivityIndicator, Snackbar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { getProducts, addProduct } from '../services/api';

const Header = () => (
  <View style={styles.headerContainer}>
    <View style={styles.headerContent}>
      <Text style={styles.headerTitle}>Hi, Ilko</Text>
      <View style={styles.headerIcons}>
        <IconButton
          icon="calculator"
          size={24}
          iconColor="#666"
          onPress={() => {}}
        />
        <IconButton
          icon="help-circle-outline"
          size={24}
          iconColor="#666"
          onPress={() => {}}
        />
        <IconButton
          icon="cog"
          size={24}
          iconColor="#666"
          onPress={() => {}}
        />
      </View>
    </View>
  </View>
);

const InventoryScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const [newProduct, setNewProduct] = useState({
    name: '',
    sku: '',
    price: '',
    initial_stock: '',
    category_id: '',
    supplier_id: '',
    description: ''
  });

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await getProducts();
      setProducts(Array.isArray(response) ? response : []);
      setError(null);
    } catch (err) {
      setError('Failed to fetch products');
      showSnackbar('Failed to load products');
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleAddProduct = async () => {
    try {
      if (!newProduct.name || !newProduct.price || !newProduct.category_id || !newProduct.supplier_id || !newProduct.initial_stock) {
        showSnackbar('Please fill all required fields');
        return;
      }

      const productData = {
        ...newProduct,
        price: parseFloat(newProduct.price),
        initial_stock: parseInt(newProduct.initial_stock),
        category_id: parseInt(newProduct.category_id),
        supplier_id: parseInt(newProduct.supplier_id)
      };

      setLoading(true);
      await addProduct(productData);
      setShowAddModal(false);
      showSnackbar('Product added successfully');
      fetchProducts();
      resetForm();
    } catch (err) {
      showSnackbar(err.message || 'Failed to add product');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setNewProduct({
      name: '',
      sku: '',
      price: '',
      initial_stock: '',
      category_id: '',
      supplier_id: '',
      description: ''
    });
  };

  const showSnackbar = (message) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  return (
    <SafeAreaView style={styles.container}>
     
      <View style={styles.content}>
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder="Search products"
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchBar}
          />
        </View>

        {loading && !showAddModal ? (
          <ActivityIndicator style={styles.loader} />
        ) : (
          <ScrollView>
            {products.map((product) => (
              <Card key={product.id} style={styles.productCard}>
                <Card.Content>
                  <View style={styles.productRow}>
                    <View style={styles.productInfo}>
                      <Text style={styles.productName}>{product.name}</Text>
                      <Text style={styles.productSku}>SKU: {product.sku || 'N/A'}</Text>
                    </View>
                    <View style={styles.productPricing}>
                      <Text style={styles.price}>৳{product.price}</Text>
                      <Text style={styles.stock}>Stock: {product.initial_stock || 0}</Text>
                    </View>
                  </View>
                </Card.Content>
              </Card>
            ))}
          </ScrollView>
        )}

        <Button
          mode="contained"
          icon="plus"
          onPress={() => setShowAddModal(true)}
          style={styles.addButton}
        >
          Add Product
        </Button>

        <Portal>
          <Modal
            visible={showAddModal}
            onDismiss={() => setShowAddModal(false)}
            contentContainerStyle={styles.modalContainer}
          >
            <ScrollView>
              <Text style={styles.modalTitle}>Add New Product</Text>

              <TextInput
                label="Product Name *"
                value={newProduct.name}
                onChangeText={text => setNewProduct({...newProduct, name: text})}
                style={styles.input}
              />

              <TextInput
                label="SKU"
                value={newProduct.sku}
                onChangeText={text => setNewProduct({...newProduct, sku: text})}
                style={styles.input}
              />

              <TextInput
                label="Price *"
                value={newProduct.price}
                onChangeText={text => setNewProduct({...newProduct, price: text})}
                keyboardType="numeric"
                style={styles.input}
              />

              <TextInput
                label="Initial Stock *"
                value={newProduct.initial_stock}
                onChangeText={text => setNewProduct({...newProduct, initial_stock: text})}
                keyboardType="numeric"
                style={styles.input}
              />

              <TextInput
                label="Category ID *"
                value={newProduct.category_id}
                onChangeText={text => setNewProduct({...newProduct, category_id: text})}
                keyboardType="numeric"
                style={styles.input}
              />

              <TextInput
                label="Supplier ID *"
                value={newProduct.supplier_id}
                onChangeText={text => setNewProduct({...newProduct, supplier_id: text})}
                keyboardType="numeric"
                style={styles.input}
              />

              <TextInput
                label="Description"
                value={newProduct.description}
                onChangeText={text => setNewProduct({...newProduct, description: text})}
                multiline
                style={styles.input}
              />

              <View style={styles.modalButtons}>
                <Button onPress={() => setShowAddModal(false)} style={styles.modalButton}>
                  Cancel
                </Button>
                <Button 
                  mode="contained" 
                  onPress={handleAddProduct}
                  loading={loading}
                  style={styles.modalButton}
                >
                  Save
                </Button>
              </View>
            </ScrollView>
          </Modal>
        </Portal>

        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
        >
          {snackbarMessage}
        </Snackbar>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  headerContainer: {
    backgroundColor: '#fff',
    paddingTop: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    elevation: 2,
    zIndex: 1000,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    padding: 16,
  },
  searchBar: {
    elevation: 0,
  },
  loader: {
    marginTop: 20,
  },
  productCard: {
    margin: 8,
  },
  productRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  productSku: {
    fontSize: 12,
    color: '#666',
  },
  productPricing: {
    alignItems: 'flex-end',
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  stock: {
    fontSize: 12,
    color: '#666',
  },
  addButton: {
    margin: 16,
    backgroundColor: '#4CAF50',
  },
  modalContainer: {
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
    backgroundColor: 'transparent',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  modalButton: {
    marginLeft: 8,
  }
});

export default InventoryScreen; 