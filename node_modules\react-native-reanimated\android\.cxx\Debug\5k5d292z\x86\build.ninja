# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Reanimated
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android\.cxx\Debug\5k5d292z\x86" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android\.cxx\Debug\5k5d292z\x86" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android" -B"C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android\.cxx\Debug\5k5d292z\x86""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target worklets


#############################################
# Order-only phony target for worklets

build cmake_object_order_depends_target_worklets: phony || src/main/cpp/worklets/CMakeFiles/worklets.dir

build src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\NativeModules\WorkletsModuleProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\NativeModules
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\NativeModules\WorkletsModuleProxySpec.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\NativeModules
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\Registries\EventHandlerRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\Registries
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\Registries\WorkletRuntimeRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\Registries
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/4390285b2d5f301878976b97edb28644/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\4390285b2d5f301878976b97edb28644\react-native-reanimated\Common\cpp\worklets\SharedItems\Shareables.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\4390285b2d5f301878976b97edb28644\react-native-reanimated\Common\cpp\worklets\SharedItems
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/4390285b2d5f301878976b97edb28644/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\4390285b2d5f301878976b97edb28644\react-native-reanimated\Common\cpp\worklets\Tools\AsyncQueue.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\4390285b2d5f301878976b97edb28644\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/4390285b2d5f301878976b97edb28644/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\4390285b2d5f301878976b97edb28644\react-native-reanimated\Common\cpp\worklets\Tools\JSISerializer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\4390285b2d5f301878976b97edb28644\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/xampp/htdocs/calc_-_Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\xampp\htdocs\calc_-_Copy\node_modules\react-native-reanimated\Common\cpp\worklets\Tools\JSLogger.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\xampp\htdocs\calc_-_Copy\node_modules\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/4390285b2d5f301878976b97edb28644/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\4390285b2d5f301878976b97edb28644\react-native-reanimated\Common\cpp\worklets\Tools\JSScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\4390285b2d5f301878976b97edb28644\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\Tools\ReanimatedJSIUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\Tools\ReanimatedVersion.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/4390285b2d5f301878976b97edb28644/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\4390285b2d5f301878976b97edb28644\react-native-reanimated\Common\cpp\worklets\Tools\UIScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\4390285b2d5f301878976b97edb28644\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\Tools\WorkletEventHandler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\WorkletRuntime\RNRuntimeWorkletDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\WorkletRuntime\ReanimatedHermesRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\WorkletRuntime\ReanimatedRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\WorkletRuntime\WorkletRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\WorkletRuntime\WorkletRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/AndroidUIScheduler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\android\AndroidUIScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/PlatformLogger.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\android\PlatformLogger.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsModule.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\android\WorkletsModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o: CXX_COMPILER__worklets_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsOnLoad.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\android\WorkletsOnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target worklets


#############################################
# Link the shared library ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.so

build ../../../../build/intermediates/cxx/Debug/5k5d292z/obj/x86/libworklets.so: CXX_SHARED_LIBRARY_LINKER__worklets_Debug src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/4390285b2d5f301878976b97edb28644/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/4390285b2d5f301878976b97edb28644/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/4390285b2d5f301878976b97edb28644/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/xampp/htdocs/calc_-_Copy/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/4390285b2d5f301878976b97edb28644/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/4390285b2d5f301878976b97edb28644/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/libs/android.x86/libhermes.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/libs/android.x86/libhermestooling.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = -llog  C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/libs/android.x86/libhermes.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/libs/android.x86/libhermestooling.so  -latomic -lm
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libworklets.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_FILE = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.so
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libworklets.pdb


#############################################
# Utility command for edit_cache

build src/main/cpp/worklets/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android\.cxx\Debug\5k5d292z\x86\src\main\cpp\worklets" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build src/main/cpp/worklets/edit_cache: phony src/main/cpp/worklets/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/main/cpp/worklets/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android\.cxx\Debug\5k5d292z\x86\src\main\cpp\worklets" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android" -B"C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android\.cxx\Debug\5k5d292z\x86""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/main/cpp/worklets/rebuild_cache: phony src/main/cpp/worklets/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target reanimated


#############################################
# Order-only phony target for reanimated

build cmake_object_order_depends_target_reanimated: phony || cmake_object_order_depends_target_worklets

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\AnimatedSensor\AnimatedSensorModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\AnimatedSensor
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Fabric/PropsRegistry.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\Fabric\PropsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\Fabric\ReanimatedCommitHook.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\Fabric\ReanimatedMountHook.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\Fabric\ShadowTreeCloner.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\LayoutAnimations\LayoutAnimationsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\LayoutAnimations
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\LayoutAnimations\LayoutAnimationsProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\LayoutAnimations
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\LayoutAnimations\LayoutAnimationsUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\LayoutAnimations
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\NativeModules\ReanimatedModuleProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\NativeModules
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\NativeModules\ReanimatedModuleProxySpec.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\NativeModules
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\RuntimeDecorators\RNRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\RuntimeDecorators
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\RuntimeDecorators\UIRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\RuntimeDecorators
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Tools/FeaturesConfig.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/FeaturesConfig.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\Tools\FeaturesConfig.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\9e5f2046f2042b7d315a3c844fef389e\Common\cpp\reanimated\Tools
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/JNIHelper.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android\JNIHelper.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/LayoutAnimations.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android\LayoutAnimations.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/NativeProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android\NativeProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o: CXX_COMPILER__reanimated_Debug C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/OnLoad.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/callinvoker" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/yoga" -I"C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target reanimated


#############################################
# Link the shared library ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.so

build ../../../../build/intermediates/cxx/Debug/5k5d292z/obj/x86/libreanimated.so: CXX_SHARED_LIBRARY_LINKER__reanimated_Debug src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Fabric/PropsRegistry.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/9e5f2046f2042b7d315a3c844fef389e/Common/cpp/reanimated/Tools/FeaturesConfig.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o | ../../../../build/intermediates/cxx/Debug/5k5d292z/obj/x86/libworklets.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/libs/android.x86/libhermes.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/libs/android.x86/libhermestooling.so || ../../../../build/intermediates/cxx/Debug/5k5d292z/obj/x86/libworklets.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = ../../../../build/intermediates/cxx/Debug/5k5d292z/obj/x86/libworklets.so  -landroid  C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so  -llog  C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/1c52e289df292dc4688ccb75a4fa9a38/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/1cbc471af91dd7cd77a14280d219e183/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/libs/android.x86/libhermes.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/8eda7ef8193d39658292351753e97397/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/libs/android.x86/libhermestooling.so  -latomic -lm
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreanimated.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_FILE = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.so
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\5k5d292z\obj\x86\libreanimated.pdb


#############################################
# Utility command for edit_cache

build src/main/cpp/reanimated/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android\.cxx\Debug\5k5d292z\x86\src\main\cpp\reanimated" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build src/main/cpp/reanimated/edit_cache: phony src/main/cpp/reanimated/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/main/cpp/reanimated/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android\.cxx\Debug\5k5d292z\x86\src\main\cpp\reanimated" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android" -B"C:\xampp\htdocs\calc - Copy\node_modules\react-native-reanimated\android\.cxx\Debug\5k5d292z\x86""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/main/cpp/reanimated/rebuild_cache: phony src/main/cpp/reanimated/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build libreanimated.so: phony ../../../../build/intermediates/cxx/Debug/5k5d292z/obj/x86/libreanimated.so

build libworklets.so: phony ../../../../build/intermediates/cxx/Debug/5k5d292z/obj/x86/libworklets.so

build reanimated: phony ../../../../build/intermediates/cxx/Debug/5k5d292z/obj/x86/libreanimated.so

build worklets: phony ../../../../build/intermediates/cxx/Debug/5k5d292z/obj/x86/libworklets.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86

build all: phony src/main/cpp/worklets/all src/main/cpp/reanimated/all

# =============================================================================

#############################################
# Folder: C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/src/main/cpp/reanimated

build src/main/cpp/reanimated/all: phony ../../../../build/intermediates/cxx/Debug/5k5d292z/obj/x86/libreanimated.so

# =============================================================================

#############################################
# Folder: C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/src/main/cpp/worklets

build src/main/cpp/worklets/all: phony ../../../../build/intermediates/cxx/Debug/5k5d292z/obj/x86/libworklets.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/CMakeFiles/cmake.verify_globs | ../../../../CMakeLists.txt ../../../../src/main/cpp/reanimated/CMakeLists.txt ../../../../src/main/cpp/worklets/CMakeLists.txt ../prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfigVersion.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/hermes-engine/hermes-engineConfig.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/hermes-engine/hermes-engineConfigVersion.cmake C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../../../../CMakeLists.txt ../../../../src/main/cpp/reanimated/CMakeLists.txt ../../../../src/main/cpp/worklets/CMakeLists.txt ../prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfigVersion.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/hermes-engine/hermes-engineConfig.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/hermes-engine/hermes-engineConfigVersion.cmake C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native-reanimated/android/.cxx/Debug/5k5d292z/x86/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/xampp/htdocs/calc$ -$ Copy/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
