import React, { createContext, useContext, useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { InventoryItem } from '../types';

type InventoryContextType = {
  items: InventoryItem[];
  loading: boolean;
  addItem: (item: InventoryItem) => Promise<void>;
  updateItem: (id: string, updates: Partial<InventoryItem>) => Promise<void>;
  deleteItem: (id: string) => Promise<void>;
};

const InventoryContext = createContext<InventoryContextType>(null!);

export const InventoryProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadInventory = async () => {
      try {
        const stored = await AsyncStorage.getItem('inventory');
        setItems(stored ? JSON.parse(stored) : []);
      } catch (error) {
        console.error('Failed to load inventory:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadInventory();
  }, []);

  const saveInventory = async (newItems: InventoryItem[]) => {
    try {
      await AsyncStorage.setItem('inventory', JSON.stringify(newItems));
      setItems(newItems);
    } catch (error) {
      console.error('Failed to save inventory:', error);
    }
  };

  const addItem = async (item: InventoryItem) => {
    const newItems = [...items, item];
    await saveInventory(newItems);
  };

  const updateItem = async (id: string, updates: Partial<InventoryItem>) => {
    const newItems = items.map(item => 
      item.id === id ? { ...item, ...updates } : item
    );
    await saveInventory(newItems);
  };

  const deleteItem = async (id: string) => {
    const newItems = items.filter(item => item.id !== id);
    await saveInventory(newItems);
  };

  return (
    <InventoryContext.Provider value={{ items, loading, addItem, updateItem, deleteItem }}>
      {children}
    </InventoryContext.Provider>
  );
};

export const useInventory = () => useContext(InventoryContext); 