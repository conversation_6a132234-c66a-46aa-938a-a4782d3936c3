# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/RNDateTimePickerCGen-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/ComponentDescriptors.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/Props.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/pagerview-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/ComponentDescriptors.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/EventEmitters.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/Props.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/ShadowNodes.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/States.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/pagerviewJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/rnpdf-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/ComponentDescriptors.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/EventEmitters.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/Props.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/ShadowNodes.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/States.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/rnpdfJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/safeareacontext-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/utils/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:24 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/RNShareSpec-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec/ComponentDescriptors.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec/EventEmitters.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec/Props.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec/RNShareSpecJSI-generated.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec/ShadowNodes.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/RNVectorIconsSpec-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/Props.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp"
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:42 (file)
# input_SRC at C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "C:/xampp/htdocs/calc - Copy/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/xampp/htdocs/calc - Copy/android/app/.cxx/Debug/39285h5l/x86_64/CMakeFiles/cmake.verify_globs")
endif()
